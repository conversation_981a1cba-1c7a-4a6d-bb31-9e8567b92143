CREATE TABLE companies (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  ticker VARCHAR(10) NOT NULL UNIQUE
);

CREATE TABLE company_data (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id),
  -- Store trailing 5 years as JSONB (e.g., {"2020": 1000, "2021": 1100, ...}) or just {"TTM": 1200}
  revenue JSONB,
  revenue_growth_rate_fwd NUMERIC,
  revenue_growth_rate_trailing JSONB, -- optional, if you want to store trailing growth rates
  ebitda JSONB,
  ebitda_growth_rate_fwd NUMERIC,
  ebitda_growth_rate_trailing JSONB,
  depreciation_amortization JSONB,
  ebit JSONB,
  capex JSONB,
  working_capital JSONB,
  tax_rate NUMERIC,
  levered_fcf JSONB,
  wacc NUMERIC,
  debt_to_equity_ratio NUMERIC,
  current_ratio NUMERIC,
  quick_ratio NUMERIC,
  gross_profit_margin NUMERIC,
  pe_ratio NUMERIC,
  eps NUMERIC,
  ps_ratio NUMERIC,
  dividend_yield_percentage NUMERIC,
  ev_to_ebitda NUMERIC,
  net_income JSONB,
  sentiment_data TEXT DEFAULT '',
  -- Missing columns that the code expects
  price NUMERIC(10,2),
  volume BIGINT,
  market_cap BIGINT,
  beta NUMERIC(5,2),
  fifty_two_week_high NUMERIC(10,2),
  fifty_two_week_low NUMERIC(10,2),
  moving_avg_50 NUMERIC(10,2),
  moving_avg_200 NUMERIC(10,2),
  rsi_14 NUMERIC(5,2),
  macd NUMERIC(10,4),
  bollinger_upper NUMERIC(10,2),
  bollinger_lower NUMERIC(10,2),
  sentiment_score NUMERIC(5,2),
  news_count INTEGER DEFAULT 0,
  as_of_date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Neural Network Predictions Table
CREATE TABLE neural_network_predictions (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id),
  company_data_id INTEGER REFERENCES company_data(id),
  prediction_type VARCHAR(50) NOT NULL, -- 'STRONG_BUY', 'BUY', 'HOLD', 'SELL', 'STRONG_SELL'
  confidence_score NUMERIC(5,2) NOT NULL, -- 0.00 to 100.00
  adjusted_confidence_score NUMERIC(5,2), -- Confidence adjusted for data quality
  predicted_price_change NUMERIC(10,4), -- Predicted percentage change
  timeframe_days INTEGER NOT NULL, -- Prediction timeframe in days
  prediction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  target_date TIMESTAMP NOT NULL, -- When to evaluate the prediction

  -- Data Quality Assessment Fields
  data_quality VARCHAR(20), -- 'excellent', 'good', 'fair', 'poor', 'very_poor', 'insufficient'
  confidence_multiplier NUMERIC(3,2), -- Quality-based confidence adjustment (0.1 to 1.0)
  missing_data_flags JSONB, -- JSON object with missing field categories
  prediction_reliability VARCHAR(20), -- 'high', 'good', 'moderate', 'low', 'very_low', 'unreliable'
  should_update_when_data_improves BOOLEAN DEFAULT FALSE, -- Flag for re-prediction when data improves
  actual_outcome VARCHAR(50), -- Actual result after target_date
  actual_price_change NUMERIC(10,4), -- Actual percentage change
  prediction_accuracy BOOLEAN, -- TRUE if prediction was correct
  reward_score NUMERIC(5,2), -- Reward/penalty score for learning
  model_version VARCHAR(50) NOT NULL DEFAULT 'production_v1',
  features_used JSONB, -- Store the features used for this prediction
  prediction_metadata JSONB, -- Additional prediction data
  evaluated_at TIMESTAMP, -- When the prediction was evaluated
  -- Missing columns that the code expects
  current_price NUMERIC(10,2),
  predicted_price NUMERIC(10,2),
  price_change NUMERIC(10,2),
  price_change_percent NUMERIC(10,4),
  accuracy_score NUMERIC(5,2),
  is_hft BOOLEAN DEFAULT FALSE,
  raw_prediction_data JSONB, -- Raw prediction data for debugging
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Neural Network Training Metrics
CREATE TABLE neural_network_metrics (
  id SERIAL PRIMARY KEY,
  model_version VARCHAR(50) NOT NULL,
  training_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  accuracy NUMERIC(5,4), -- Overall accuracy (0.0000 to 1.0000)
  precision_score NUMERIC(5,4),
  recall_score NUMERIC(5,4),
  f1_score NUMERIC(5,4),
  total_predictions INTEGER DEFAULT 0,
  correct_predictions INTEGER DEFAULT 0,
  training_samples INTEGER,
  validation_samples INTEGER,
  epochs_trained INTEGER,
  loss_value NUMERIC(10,6),
  validation_loss NUMERIC(10,6),
  hyperparameters JSONB,
  performance_by_class JSONB, -- Performance breakdown by prediction class
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Learning Feedback Table for Neural Network Training
CREATE TABLE learning_feedback (
  id SERIAL PRIMARY KEY,
  prediction_id INTEGER NOT NULL REFERENCES neural_network_predictions(id),
  predicted_price NUMERIC NOT NULL,
  actual_price NUMERIC NOT NULL,
  price_error NUMERIC NOT NULL, -- Percentage error
  direction_correct BOOLEAN NOT NULL,
  confidence NUMERIC,
  timeframe_days INTEGER NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  prediction_date TIMESTAMP NOT NULL,
  evaluation_date TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Stock Price History for Evaluation
CREATE TABLE stock_price_history (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id),
  price NUMERIC(10,4) NOT NULL,
  volume BIGINT,
  market_cap BIGINT,
  price_date DATE NOT NULL,
  data_source VARCHAR(50) DEFAULT 'yfinance',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(company_id, price_date)
);

-- Indexes for performance
CREATE INDEX idx_company_data_company_id ON company_data(company_id);
CREATE INDEX idx_company_data_date ON company_data(as_of_date);
CREATE INDEX idx_predictions_company_id ON neural_network_predictions(company_id);
CREATE INDEX idx_predictions_date ON neural_network_predictions(prediction_date);
CREATE INDEX idx_predictions_target_date ON neural_network_predictions(target_date);
CREATE INDEX idx_predictions_evaluation ON neural_network_predictions(target_date, actual_outcome);
CREATE INDEX idx_stock_price_company_date ON stock_price_history(company_id, price_date);
CREATE INDEX idx_metrics_model_version ON neural_network_metrics(model_version);
CREATE INDEX idx_learning_feedback_prediction ON learning_feedback(prediction_id);
CREATE INDEX idx_learning_feedback_symbol ON learning_feedback(symbol, evaluation_date);

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_company_data_updated_at BEFORE UPDATE ON company_data
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();