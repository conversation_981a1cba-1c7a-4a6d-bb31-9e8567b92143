"""
StockTrek Database Service
Handles all database operations with bulletproof error handling and intelligent missing data management
"""

import psycopg2
import psycopg2.extras
import pandas as pd
import json
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Optional, Tuple, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataQualityAssessment:
    """
    Comprehensive data quality assessment for bulletproof predictions
    """

    # Critical fields that must be present for any prediction
    CRITICAL_FIELDS = {
        'price', 'volume', 'market_cap', 'pe_ratio'
    }

    # Important fields that significantly improve prediction quality
    IMPORTANT_FIELDS = {
        'eps', 'dividend_yield', 'beta', 'revenue', 'ebitda',
        'debt_to_equity_ratio', 'current_ratio', 'gross_profit_margin'
    }

    # Technical indicators that enhance prediction accuracy
    TECHNICAL_FIELDS = {
        'moving_avg_50', 'moving_avg_200', 'rsi_14', 'macd',
        'bollinger_upper', 'bollinger_lower', 'volatility'
    }

    # Sentiment and news data
    SENTIMENT_FIELDS = {
        'sentiment_score', 'news_count', 'sentiment_data'
    }

    @classmethod
    def assess_data_completeness(cls, data: Dict) -> Dict[str, Any]:
        """
        Comprehensive assessment of data quality and completeness
        Returns detailed analysis for neural network decision making
        """
        assessment = {
            'overall_quality': 'unknown',
            'confidence_multiplier': 1.0,
            'missing_critical': [],
            'missing_important': [],
            'missing_technical': [],
            'missing_sentiment': [],
            'data_age_hours': 0,
            'should_wait_for_better_data': False,
            'prediction_reliability': 'unknown',
            'recommended_action': 'proceed'
        }

        try:
            # Check critical fields
            missing_critical = [field for field in cls.CRITICAL_FIELDS if not cls._has_valid_value(data.get(field))]
            assessment['missing_critical'] = missing_critical

            # Check important fields
            missing_important = [field for field in cls.IMPORTANT_FIELDS if not cls._has_valid_value(data.get(field))]
            assessment['missing_important'] = missing_important

            # Check technical fields
            missing_technical = [field for field in cls.TECHNICAL_FIELDS if not cls._has_valid_value(data.get(field))]
            assessment['missing_technical'] = missing_technical

            # Check sentiment fields
            missing_sentiment = [field for field in cls.SENTIMENT_FIELDS if not cls._has_valid_value(data.get(field))]
            assessment['missing_sentiment'] = missing_sentiment

            # Calculate data age
            timestamp = data.get('timestamp') or data.get('as_of_date')
            if timestamp:
                if isinstance(timestamp, str):
                    try:
                        timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except:
                        timestamp = datetime.now()
                elif isinstance(timestamp, date):
                    timestamp = datetime.combine(timestamp, datetime.min.time())

                assessment['data_age_hours'] = (datetime.now() - timestamp).total_seconds() / 3600

            # Determine overall quality and recommendations
            assessment.update(cls._determine_quality_level(missing_critical, missing_important, missing_technical, missing_sentiment, assessment['data_age_hours']))

            return assessment

        except Exception as e:
            logger.error(f"Error in data quality assessment: {e}")
            assessment['overall_quality'] = 'error'
            assessment['recommended_action'] = 'retry'
            return assessment

    @classmethod
    def _has_valid_value(cls, value) -> bool:
        """Check if a value is valid (not None, NaN, empty, etc.)"""
        if value is None:
            return False
        if isinstance(value, (int, float)):
            return not (pd.isna(value) if hasattr(pd, 'isna') else False)
        if isinstance(value, str):
            return value.strip() not in ['', 'N/A', 'na', 'none', '-', 'null']
        if isinstance(value, (list, dict)):
            return len(value) > 0
        return True

    @classmethod
    def _determine_quality_level(cls, missing_critical, missing_important, missing_technical, missing_sentiment, data_age_hours) -> Dict[str, Any]:
        """Determine the overall quality level and recommendations"""
        result = {}

        # Critical assessment
        if missing_critical:
            result['overall_quality'] = 'insufficient'
            result['confidence_multiplier'] = 0.1
            result['should_wait_for_better_data'] = True
            result['prediction_reliability'] = 'unreliable'
            result['recommended_action'] = 'wait_for_data'
            return result

        # Calculate quality score
        quality_score = 100
        quality_score -= len(missing_important) * 10  # -10 per missing important field
        quality_score -= len(missing_technical) * 5   # -5 per missing technical field
        quality_score -= len(missing_sentiment) * 15  # -15 per missing sentiment field

        # Age penalty
        if data_age_hours > 24:
            quality_score -= min(20, data_age_hours - 24)  # Penalty for old data

        # Determine quality level
        if quality_score >= 90:
            result['overall_quality'] = 'excellent'
            result['confidence_multiplier'] = 1.0
            result['prediction_reliability'] = 'high'
        elif quality_score >= 75:
            result['overall_quality'] = 'good'
            result['confidence_multiplier'] = 0.9
            result['prediction_reliability'] = 'good'
        elif quality_score >= 60:
            result['overall_quality'] = 'fair'
            result['confidence_multiplier'] = 0.7
            result['prediction_reliability'] = 'moderate'
        elif quality_score >= 40:
            result['overall_quality'] = 'poor'
            result['confidence_multiplier'] = 0.5
            result['prediction_reliability'] = 'low'
        else:
            result['overall_quality'] = 'very_poor'
            result['confidence_multiplier'] = 0.3
            result['prediction_reliability'] = 'very_low'

        # Determine if we should wait for better data
        if missing_sentiment and data_age_hours < 1:  # Fresh data but missing sentiment
            result['should_wait_for_better_data'] = True
            result['recommended_action'] = 'wait_for_sentiment'
        elif quality_score < 50:
            result['should_wait_for_better_data'] = True
            result['recommended_action'] = 'wait_for_better_data'
        else:
            result['should_wait_for_better_data'] = False
            result['recommended_action'] = 'proceed'

        return result


class DatabaseService:
    """
    Core database service for StockTrek
    Handles connections, queries, and data operations
    """
    
    def __init__(self, db_config: Dict = None):
        """Initialize database service with configuration"""
        self.db_config = db_config or {
            'dbname': 'stocktrek_mainbranch',
            'user': 'stocktrek_admin',
            'password': 'equity_FR',
            'host': 'localhost',
            'port': '5432'
        }
        self.connection = None
        
    def get_connection(self):
        """Get database connection with error handling"""
        try:
            if self.connection is None or self.connection.closed:
                self.connection = psycopg2.connect(**self.db_config)
                self.connection.autocommit = True
            return self.connection
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return None
    
    def execute_query(self, query: str, params: Tuple = None, fetch: bool = False):
        """Execute a database query with error handling"""
        try:
            conn = self.get_connection()
            if not conn:
                return None
                
            with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
                cur.execute(query, params)
                
                if fetch:
                    return cur.fetchall()
                return True
                
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            return None
    
    def get_company_by_symbol(self, symbol: str) -> Optional[Dict]:
        """Get company information by stock symbol/ticker"""
        query = """
            SELECT id, name, ticker
            FROM companies
            WHERE ticker = %s
        """
        result = self.execute_query(query, (symbol,), fetch=True)
        return result[0] if result else None
    
    def insert_company(self, name: str, ticker: str) -> Optional[int]:
        """Insert a new company and return its ID"""
        query = """
            INSERT INTO companies (name, ticker)
            VALUES (%s, %s)
            ON CONFLICT (ticker) DO UPDATE SET
                name = EXCLUDED.name
            RETURNING id
        """
        result = self.execute_query(query, (name, ticker), fetch=True)
        return result[0]['id'] if result else None
    
    def safe_numeric(self, value, default=None):
        """Safely extract numeric values with bulletproof handling"""
        if value is None:
            return default
        try:
            if isinstance(value, (int, float)):
                return float(value) if not (pd.isna(value) if hasattr(pd, 'isna') else False) else default
            if isinstance(value, str):
                cleaned = value.replace(',', '').replace('$', '').replace('%', '').strip()
                if cleaned.lower() in ['n/a', 'na', 'none', '', '-', 'null']:
                    return default
                return float(cleaned)
            return default
        except (ValueError, TypeError, AttributeError):
            return default

    def safe_text(self, value, default=''):
        """Safely extract text values with bulletproof handling"""
        if value is None:
            return default
        try:
            if isinstance(value, str):
                return value.strip()
            elif isinstance(value, (int, float)):
                return str(value)
            elif isinstance(value, dict):
                return json.dumps(value)
            elif isinstance(value, list):
                return json.dumps(value)
            else:
                return str(value)
        except Exception:
            return default

    def insert_company_data(self, company_id: int, data: Dict) -> Optional[int]:
        """Insert company financial data with bulletproof handling of missing values"""

        query = """
            INSERT INTO company_data (
                company_id, pe_ratio, eps, dividend_yield_percentage, beta,
                fifty_two_week_high, fifty_two_week_low, moving_avg_50, moving_avg_200,
                rsi_14, macd, bollinger_upper, bollinger_lower, sentiment_score,
                news_count, sentiment_data, as_of_date, current_price, volume,
                market_cap
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
            ) RETURNING id
        """

        params = (
            company_id,
            self.safe_numeric(data.get('pe_ratio')),
            self.safe_numeric(data.get('eps')),
            self.safe_numeric(data.get('dividend_yield')),
            self.safe_numeric(data.get('beta')),
            self.safe_numeric(data.get('fifty_two_week_high')),
            self.safe_numeric(data.get('fifty_two_week_low')),
            self.safe_numeric(data.get('moving_avg_50')),
            self.safe_numeric(data.get('moving_avg_200')),
            self.safe_numeric(data.get('rsi_14')),
            self.safe_numeric(data.get('macd')),
            self.safe_numeric(data.get('bollinger_upper')),
            self.safe_numeric(data.get('bollinger_lower')),
            self.safe_numeric(data.get('sentiment_score')),
            self.safe_numeric(data.get('news_count')),
            self.safe_text(data.get('sentiment_data', '')),
            data.get('as_of_date', date.today()),
            self.safe_numeric(data.get('current_price') or data.get('price')),
            self.safe_numeric(data.get('volume')),
            self.safe_numeric(data.get('market_cap'))
        )

        result = self.execute_query(query, params, fetch=True)
        return result[0]['id'] if result else None
    
    def store_prediction(self, company_id: int, prediction_data: Dict) -> bool:
        """Store neural network prediction with numerical price data"""
        query = """
            INSERT INTO neural_network_predictions (
                company_id, prediction_type, confidence_score,
                prediction_date, target_date, model_version,
                current_price, predicted_price, price_change, price_change_percent,
                timeframe_days, raw_prediction_data
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        timeframe_days = prediction_data.get('timeframe_days', 7)
        target_date = datetime.now() + timedelta(days=timeframe_days)

        params = (
            company_id,
            prediction_data.get('prediction', 'UNKNOWN'),
            prediction_data.get('confidence', 0),
            datetime.now(),
            target_date,
            prediction_data.get('model_version', 'v1'),
            self.safe_numeric(prediction_data.get('current_price')),
            self.safe_numeric(prediction_data.get('predicted_price')),
            self.safe_numeric(prediction_data.get('price_change')),
            self.safe_numeric(prediction_data.get('price_change_percent')),
            timeframe_days,
            json.dumps(prediction_data)
        )

        return self.execute_query(query, params) is not None
    
    def get_recent_predictions(self, days_back: int = 7) -> List[Dict]:
        """Get recent predictions for evaluation"""
        query = """
            SELECT p.*, c.ticker as symbol, c.name
            FROM neural_network_predictions p
            JOIN companies c ON p.company_id = c.id
            WHERE p.prediction_date >= %s
            ORDER BY p.prediction_date DESC
        """
        
        cutoff_date = datetime.now() - timedelta(days=days_back)
        return self.execute_query(query, (cutoff_date,), fetch=True) or []
    
    def get_predictions_for_evaluation(self) -> List[Dict]:
        """Get predictions ready for backtesting evaluation"""
        query = """
            SELECT p.*, c.ticker as symbol, c.name
            FROM neural_network_predictions p
            JOIN companies c ON p.company_id = c.id
            WHERE p.target_date <= %s
            AND p.actual_outcome IS NULL
            ORDER BY p.target_date ASC
        """
        
        return self.execute_query(query, (datetime.now(),), fetch=True) or []
    
    def update_prediction_outcome(self, prediction_id: int, actual_outcome: str, 
                                accuracy_score: float) -> bool:
        """Update prediction with actual outcome and accuracy"""
        query = """
            UPDATE neural_network_predictions 
            SET actual_outcome = %s, accuracy_score = %s, evaluated_at = %s
            WHERE id = %s
        """
        
        params = (actual_outcome, accuracy_score, datetime.now(), prediction_id)
        return self.execute_query(query, params) is not None
    
    def get_stock_price_history(self, company_id: int, days: int = 30) -> List[Dict]:
        """Get historical stock price data"""
        query = """
            SELECT price_date, open_price, high_price, low_price, 
                   close_price, volume, adjusted_close
            FROM stock_price_history
            WHERE company_id = %s 
            AND price_date >= %s
            ORDER BY price_date DESC
        """
        
        cutoff_date = datetime.now() - timedelta(days=days)
        return self.execute_query(query, (company_id, cutoff_date), fetch=True) or []
    
    def get_system_health_metrics(self) -> Dict:
        """Get system health and performance metrics"""
        try:
            metrics = {}
            
            # Prediction counts
            query = "SELECT COUNT(*) as total FROM neural_network_predictions"
            result = self.execute_query(query, fetch=True)
            metrics['total_predictions'] = result[0]['total'] if result else 0
            
            # Recent predictions
            query = """
                SELECT COUNT(*) as recent 
                FROM neural_network_predictions 
                WHERE prediction_date >= %s
            """
            cutoff = datetime.now() - timedelta(days=7)
            result = self.execute_query(query, (cutoff,), fetch=True)
            metrics['recent_predictions'] = result[0]['recent'] if result else 0
            
            # Accuracy metrics
            query = """
                SELECT AVG(accuracy_score) as avg_accuracy
                FROM neural_network_predictions 
                WHERE accuracy_score IS NOT NULL
            """
            result = self.execute_query(query, fetch=True)
            metrics['average_accuracy'] = float(result[0]['avg_accuracy'] or 0) if result else 0
            
            # Company count
            query = "SELECT COUNT(*) as total FROM companies"
            result = self.execute_query(query, fetch=True)
            metrics['total_companies'] = result[0]['total'] if result else 0
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting health metrics: {e}")
            return {}
    
    def test_connection(self) -> bool:
        """Test database connection"""
        try:
            result = self.execute_query("SELECT 1", fetch=True)
            return result is not None
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

    def assess_prediction_readiness(self, ticker: str) -> Dict[str, Any]:
        """
        Assess if we have sufficient data quality for a reliable prediction
        """
        try:
            # Get the most recent company data
            query = """
                SELECT cd.*, c.name, c.ticker
                FROM company_data cd
                JOIN companies c ON cd.company_id = c.id
                WHERE c.ticker = %s
                ORDER BY cd.created_at DESC
                LIMIT 1
            """

            result = self.execute_query(query, (ticker,), fetch=True)
            if not result:
                return {
                    'ready_for_prediction': False,
                    'reason': 'no_data_found',
                    'assessment': DataQualityAssessment.assess_data_completeness({})
                }

            data = dict(result[0])
            assessment = DataQualityAssessment.assess_data_completeness(data)

            return {
                'ready_for_prediction': assessment['recommended_action'] == 'proceed',
                'reason': assessment['recommended_action'],
                'assessment': assessment,
                'data': data
            }

        except Exception as e:
            logger.error(f"Error assessing prediction readiness for {ticker}: {e}")
            return {
                'ready_for_prediction': False,
                'reason': 'error',
                'assessment': {'overall_quality': 'error'}
            }

    def close_connection(self):
        """Close database connection"""
        if self.connection and not self.connection.closed:
            self.connection.close()
            self.connection = None
