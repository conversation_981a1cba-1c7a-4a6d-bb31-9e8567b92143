#!/usr/bin/env python3
"""
StockTrek Main Application
Production-grade autonomous stock prediction system

Usage:
    python3 main.py                                    # Start autonomous mode with daemon
    python3 main.py --predict TICKER --timeframe DAYS  # Manual prediction
"""

import sys
import os
import argparse
import logging
import signal
import time
from datetime import datetime

# Add paths for imports
sys.path.append(os.path.dirname(__file__))

from ETL.prediction_service import PredictionService
from ETL.autonomous_training_manager import AutonomousTrainingManager
from Neural_Network.neural_network_service import NeuralNetworkService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stocktrek.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StockTrekSystem:
    """
    Main StockTrek system orchestrator
    Manages all components and provides unified interface
    """
    
    def __init__(self):
        """Initialize the StockTrek system"""
        logger.info("🚀 Initializing StockTrek System")
        
        try:
            self.prediction_service = PredictionService()
            self.neural_network = NeuralNetworkService()
            self.autonomous_training_manager = AutonomousTrainingManager()

            self.is_running = False
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("✅ StockTrek system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize StockTrek system: {e}")
            raise
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"📡 Received signal {signum}, shutting down gracefully...")
        self.shutdown()
        sys.exit(0)
    
    def start_autonomous_mode(self):
        """Start the system in autonomous mode with daemon"""
        try:
            logger.info("🤖 Starting StockTrek Autonomous Mode with Daemon")
            logger.info("📊 System will continuously scan markets and make predictions")
            logger.info("🔄 Backtesting will evaluate predictions automatically")
            logger.info("⏹️  Press Ctrl+C to stop")

            # Start autonomous training system
            logger.info("🚀 Starting autonomous training system...")
            if not self.autonomous_training_manager.start_autonomous_training():
                logger.error("❌ Failed to start autonomous training system")
                return False

            logger.info("✅ Autonomous training system started")
            self.is_running = True

            # Main loop with progress reporting
            while self.is_running:
                try:
                    # Report training progress every 5 minutes
                    progress = self.autonomous_training_manager.get_training_progress()
                    if progress and 'progress_percent' in progress:
                        logger.info(f"📊 Training Progress: {progress['progress_percent']:.1f}% complete")

                        # Check if training cycle is complete
                        if progress.get('training_cycle_completed', False):
                            logger.info("🎉 Training cycle completed for today!")

                    # Sleep for 5 minutes
                    time.sleep(300)

                except KeyboardInterrupt:
                    logger.info("🛑 Keyboard interrupt received")
                    break
                except Exception as e:
                    logger.error(f"❌ Error in main loop: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying

            return True

        except Exception as e:
            logger.error(f"❌ Error starting autonomous mode: {e}")
            return False
        finally:
            self.shutdown()

    def manual_prediction(self, ticker: str, timeframe_days: int):
        """Make a manual prediction for a specific ticker and timeframe"""
        try:
            logger.info(f"🎯 Making manual prediction for {ticker} ({timeframe_days} days)")

            # Make prediction using the prediction service
            prediction = self.prediction_service.make_prediction(ticker, timeframe_days)

            if prediction:
                logger.info(f"✅ Prediction completed for {ticker}")
                logger.info(f"📈 Current Price: ${prediction.get('current_price', 'N/A')}")
                logger.info(f"🎯 Predicted Price: ${prediction.get('predicted_price', 'N/A')}")
                logger.info(f"📊 Direction: {prediction.get('direction', 'N/A')}")
                logger.info(f"🎲 Confidence: {prediction.get('confidence', 'N/A')}%")

                return prediction
            else:
                logger.error(f"❌ Failed to generate prediction for {ticker}")
                return None

        except Exception as e:
            logger.error(f"❌ Error making manual prediction: {e}")
            return None

    def shutdown(self):
        """Shutdown the system gracefully"""
        try:
            logger.info("🛑 Shutting down StockTrek system")

            self.is_running = False

            # Stop autonomous training
            if hasattr(self, 'autonomous_training_manager'):
                self.autonomous_training_manager.stop_autonomous_training()

            logger.info("✅ StockTrek system shutdown complete")

        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='StockTrek Autonomous Stock Prediction System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 main.py                                    # Start autonomous mode with daemon
  python3 main.py --predict AAPL --timeframe 5       # Manual prediction for AAPL (5 days)
  python3 main.py --export                           # Export all data to desktop for investors
        """
    )
    parser.add_argument('--predict', type=str, metavar='TICKER',
                       help='Make prediction for a specific stock symbol (e.g., AAPL, MSFT)')
    parser.add_argument('--timeframe', type=int, metavar='DAYS', default=7,
                       help='Prediction timeframe in days (default: 7)')
    parser.add_argument('--export', action='store_true',
                       help='Export all data to desktop "StockTrek Data" folder for investor verification')

    args = parser.parse_args()

    try:
        # Initialize system
        system = StockTrekSystem()

        if args.export:
            # Desktop export mode
            print("\n📦 Exporting StockTrek Data to Desktop...")
            print("   📊 Comprehensive investor verification package")
            print("   🔒 Anonymous and tamper-proof data")
            print("   📋 All predictions, training, and performance data\n")

            try:
                from ETL.desktop_export_service import DesktopExportService
                exporter = DesktopExportService()
                export_path = exporter.export_all_data(days_back=30)

                print(f"✅ Export completed successfully!")
                print(f"📁 Location: {export_path}")
                print(f"📊 Contains: All predictions, training data, and verification reports")
                print(f"💼 Ready for investor presentation and verification")

            except Exception as e:
                print(f"❌ Export failed: {e}")

        elif args.predict:
            # Manual prediction mode
            ticker = args.predict.upper()
            timeframe = args.timeframe

            print(f"\n🔍 Making prediction for {ticker} ({timeframe} days)...")
            prediction = system.manual_prediction(ticker, timeframe)

            if prediction:
                print(f"\n📊 StockTrek Prediction for {ticker}:")
                print(f"   💰 Current Price: ${prediction.get('current_price', 'N/A')}")
                print(f"   🔮 Predicted Price: ${prediction.get('predicted_price', 'N/A')}")
                print(f"   🎯 Direction: {prediction.get('direction', 'N/A')}")
                print(f"   📈 Confidence: {prediction.get('confidence', 'N/A')}%")
                print(f"   ⏰ Timeframe: {timeframe} days")
                print(f"\n✅ Prediction stored in database for backtesting")
            else:
                print(f"❌ Failed to make prediction for {ticker}")
                print("   Please check if the ticker symbol is valid and try again.")

        else:
            # Autonomous mode (default)
            print("\n🤖 Starting StockTrek Autonomous Mode with Daemon")
            print("   📊 Will scan markets and make predictions continuously")
            print("   🔄 Automatic backtesting when prediction timeframes are met")
            print("   ⏹️  Press Ctrl+C to stop\n")
            system.start_autonomous_mode()
            
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
