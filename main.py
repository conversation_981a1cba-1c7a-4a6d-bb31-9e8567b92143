#!/usr/bin/env python3
"""
StockTrek Main Application
Production-grade autonomous stock prediction system

Usage:
    python main.py                           # Start autonomous mode
    python main.py --predict AAPL            # Single prediction
    python main.py --status                  # System status
    python main.py --backtest                # Run backtesting
    python main.py --dashboard               # Prediction anomaly dashboard
    python main.py --daemon start            # Start 24/7 background daemon
    python main.py --daemon stop             # Stop background daemon
    python main.py --daemon status           # Check daemon status
    python main.py --monitor                 # Database monitoring dashboard
    python main.py --live                    # Live database monitoring
    python main.py --export complete         # Complete data export (30 days)
    python main.py --export quick            # Quick report (7 days)
    python main.py --export investor         # Investor presentation package
    python main.py --schedule-reports        # Automated report scheduler
    python main.py --hft                     # High-frequency trading mode
    python main.py --slow                    # Slow daemon mode
"""

import sys
import os
import argparse
import logging
import signal
import time
from datetime import datetime

# Add paths for imports
sys.path.append(os.path.dirname(__file__))

from ETL.prediction_service import PredictionService
from ETL.autonomous_scanner import AutonomousScanner
from ETL.backtesting_service import BacktestingService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stocktrek.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StockTrekSystem:
    """
    Main StockTrek system orchestrator
    Manages all components and provides unified interface
    """
    
    def __init__(self):
        """Initialize the StockTrek system"""
        logger.info("🚀 Initializing StockTrek System")
        
        try:
            self.prediction_service = PredictionService()
            self.autonomous_scanner = AutonomousScanner()
            self.backtesting_service = BacktestingService()
            
            self.is_running = False
            
            # Setup signal handlers for graceful shutdown
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            logger.info("✅ StockTrek system initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize StockTrek system: {e}")
            raise
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"📡 Received signal {signum}, shutting down gracefully...")
        self.shutdown()
        sys.exit(0)
    
    def start_autonomous_mode(self, parallel_mode: bool = False):
        """Start the system in autonomous mode"""
        try:
            mode_type = "parallel training" if parallel_mode else "autonomous"
            logger.info(f"🤖 Starting StockTrek in {mode_type} mode")

            # Start autonomous scanning
            if not self.autonomous_scanner.start_autonomous_scanning():
                logger.error("❌ Failed to start autonomous scanning")
                return False

            self.is_running = True

            logger.info(f"✅ StockTrek {mode_type} mode started")
            logger.info("📊 System will continuously scan markets and make predictions")
            logger.info("🔄 Backtesting will evaluate predictions automatically")
            if parallel_mode:
                logger.info("🔗 Parallel training mode - sharing learning with other instances")
            logger.info("⏹️  Press Ctrl+C to stop")

            # Main loop - keep system running and perform periodic tasks
            while self.is_running:
                try:
                    # Run backtesting cycle every hour
                    self._run_periodic_backtesting()

                    # Sleep for 1 hour
                    time.sleep(3600)

                except KeyboardInterrupt:
                    logger.info("🛑 Keyboard interrupt received")
                    break
                except Exception as e:
                    logger.error(f"❌ Error in main loop: {e}")
                    time.sleep(60)  # Wait 1 minute before retrying

            return True

        except Exception as e:
            logger.error(f"❌ Error starting autonomous mode: {e}")
            return False

    def start_hft_mode(self):
        """Start High-Frequency Trading mode"""
        try:
            import asyncio
            from ETL.hft_mode import HFTMode

            logger.info("⚡ Initializing HFT Mode")
            hft = HFTMode()

            # Run HFT mode
            asyncio.run(hft.start_hft_mode())

        except KeyboardInterrupt:
            logger.info("🛑 HFT Mode stopped by user")
        except Exception as e:
            logger.error(f"❌ Error in HFT mode: {e}")
            raise

    def start_slow_mode(self):
        """Start slow daemon mode with longer intervals"""
        try:
            logger.info("🐌 Starting StockTrek in slow daemon mode")

            # Start autonomous scanning with slower settings
            if not self.autonomous_scanner.start_autonomous_scanning():
                logger.error("❌ Failed to start slow mode scanning")
                return False

            # Configure for slower operation
            self.autonomous_scanner.scan_interval = 7200  # 2 hour intervals

            self.is_running = True

            logger.info("✅ Slow daemon mode started")
            logger.info("🐌 Using conservative intervals for stability")
            logger.info("⏹️  Press Ctrl+C to stop")

            # Main loop with longer intervals
            while self.is_running:
                try:
                    # Less frequent backtesting
                    self._run_periodic_backtesting()

                    # Sleep for 2 hours
                    time.sleep(7200)

                except KeyboardInterrupt:
                    logger.info("🛑 Keyboard interrupt received")
                    break
                except Exception as e:
                    logger.error(f"❌ Error in slow mode loop: {e}")
                    time.sleep(300)  # Wait 5 minutes before retrying

            return True

        except Exception as e:
            logger.error(f"❌ Error starting slow mode: {e}")
            return False
    
    def _run_periodic_backtesting(self):
        """Run backtesting cycle periodically"""
        try:
            logger.info("🔍 Running periodic backtesting cycle")
            results = self.backtesting_service.run_evaluation_cycle()
            
            if results['status'] == 'completed':
                logger.info(f"✅ Backtesting completed: {results['predictions_evaluated']} predictions evaluated")
                if results['predictions_evaluated'] > 0:
                    logger.info(f"📈 Average accuracy: {results['average_accuracy']:.3f}")
            else:
                logger.warning(f"⚠️ Backtesting failed: {results.get('error', 'unknown error')}")
                
        except Exception as e:
            logger.error(f"❌ Error in periodic backtesting: {e}")
    
    def make_single_prediction(self, symbol: str, timeframe_days: int = 7) -> dict:
        """Make a single prediction for a stock symbol"""
        try:
            logger.info(f"📊 Making prediction for {symbol}")
            
            prediction = self.prediction_service.make_prediction(symbol, timeframe_days)
            
            if prediction:
                logger.info(f"✅ Prediction for {symbol}: {prediction['prediction']} ({prediction['confidence']}%)")
                return prediction
            else:
                logger.error(f"❌ Failed to make prediction for {symbol}")
                return {}
                
        except Exception as e:
            logger.error(f"❌ Error making prediction for {symbol}: {e}")
            return {}
    
    def run_backtesting(self) -> dict:
        """Run a backtesting cycle"""
        try:
            logger.info("🔍 Running backtesting cycle")
            
            results = self.backtesting_service.run_evaluation_cycle()
            
            if results['status'] == 'completed':
                logger.info(f"✅ Backtesting completed: {results['predictions_evaluated']} predictions evaluated")
                if results['predictions_evaluated'] > 0:
                    logger.info(f"📈 Average accuracy: {results['average_accuracy']:.3f}")
            else:
                logger.error(f"❌ Backtesting failed: {results.get('error', 'unknown error')}")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error running backtesting: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_system_status(self) -> dict:
        """Get comprehensive system status"""
        try:
            logger.info("📋 Getting system status")
            
            # Get status from all components
            prediction_status = self.prediction_service.get_system_status()
            scanner_status = self.autonomous_scanner.get_scanner_status()
            
            # Get performance metrics
            performance = self.backtesting_service.get_performance_metrics()
            
            status = {
                'system': {
                    'status': 'operational',
                    'autonomous_mode': self.is_running,
                    'timestamp': datetime.now().isoformat()
                },
                'prediction_service': prediction_status,
                'autonomous_scanner': scanner_status,
                'performance_metrics': performance
            }
            
            logger.info("✅ System status retrieved")
            return status
            
        except Exception as e:
            logger.error(f"❌ Error getting system status: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def shutdown(self):
        """Shutdown the system gracefully"""
        try:
            logger.info("🛑 Shutting down StockTrek system")
            
            self.is_running = False
            
            # Stop autonomous scanner
            self.autonomous_scanner.stop_autonomous_scanning()
            
            # Cleanup all services
            self.autonomous_scanner.cleanup()
            self.prediction_service.cleanup()
            self.backtesting_service.cleanup()
            
            logger.info("✅ StockTrek system shutdown complete")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='StockTrek Autonomous Stock Prediction System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 main.py                           # Start autonomous mode
  python3 main.py --predict AAPL --timeframe 5   # Predict AAPL for 5 days
  python3 main.py --hft                     # Start High-Frequency Trading mode
  python3 main.py --slow                    # Start slow daemon mode
  python3 main.py --parallel                # Start parallel training mode
  python3 main.py --status                  # Show system status
  python3 main.py --backtest                # Run backtesting
  python3 main.py --dashboard               # Show prediction anomaly dashboard
        """
    )
    parser.add_argument('--predict', type=str, metavar='TICKER',
                       help='Make prediction for a specific stock symbol (e.g., AAPL, MSFT)')
    parser.add_argument('--timeframe', type=int, metavar='DAYS', default=7,
                       help='Prediction timeframe in days (default: 7, options: 1, 5, 10, 30)')
    parser.add_argument('--hft', action='store_true',
                       help='Enable High-Frequency Trading mode (5-minute predictions with rapid learning)')
    parser.add_argument('--slow', action='store_true',
                       help='Enable slow mode for daemon (longer intervals, less aggressive)')
    parser.add_argument('--parallel', action='store_true',
                       help='Enable parallel training mode (run alongside another instance)')
    parser.add_argument('--status', action='store_true', help='Show system status and health metrics')
    parser.add_argument('--backtest', action='store_true', help='Run backtesting cycle to evaluate predictions')
    parser.add_argument('--dashboard', action='store_true', help='Show prediction anomaly dashboard and validation report')
    parser.add_argument('--daemon', choices=['start', 'stop', 'restart', 'status'],
                       help='Daemon control: start/stop/restart/status 24/7 background service')
    parser.add_argument('--monitor', action='store_true', help='Launch database monitoring dashboard')
    parser.add_argument('--live', action='store_true', help='Live database monitoring mode')
    parser.add_argument('--export', choices=['complete', 'quick', 'investor'],
                       help='Export data: complete/quick/investor package')
    parser.add_argument('--export-days', type=int, default=30,
                       help='Number of days to export (default: 30)')
    parser.add_argument('--schedule-reports', action='store_true',
                       help='Start automated report scheduler')

    args = parser.parse_args()
    
    try:
        # Initialize system
        system = StockTrekSystem()
        
        if args.predict:
            # Single prediction mode
            ticker = args.predict.upper()
            timeframe = args.timeframe

            # Validate timeframe
            valid_timeframes = [1, 5, 10, 30]
            if timeframe not in valid_timeframes:
                print(f"⚠️  Warning: Timeframe {timeframe} is not standard. Recommended: {valid_timeframes}")

            print(f"\n🔍 Making prediction for {ticker} ({timeframe} days)...")
            prediction = system.make_single_prediction(ticker, timeframe)

            if prediction:
                print(f"\n📊 StockTrek Prediction for {ticker}:")
                print(f"   🎯 Direction: {prediction['prediction']}")
                print(f"   📈 Confidence: {prediction['confidence']}%")
                print(f"   💰 Current Price: ${prediction.get('current_price', 'N/A')}")
                print(f"   🔮 Predicted Price: ${prediction.get('predicted_price', 'N/A')}")
                print(f"   📊 Price Change: ${prediction.get('price_change', 'N/A')} ({prediction.get('price_change_percent', 'N/A')}%)")
                print(f"   ⏰ Timeframe: {timeframe} days")
                print(f"   📋 Data Quality: {prediction.get('data_quality', 'unknown')}")

                if prediction.get('missing_fields'):
                    print(f"   ⚠️  Missing Data: {len(prediction['missing_fields'])} fields")

                print(f"\n✅ Prediction stored in database for backtesting")
            else:
                print(f"❌ Failed to make prediction for {ticker}")
                print("   Please check if the ticker symbol is valid and try again.")
                
        elif args.status:
            # Status mode
            status = system.get_system_status()
            print("\n📋 StockTrek System Status:")
            print(f"   System: {status['system']['status']}")
            print(f"   Neural Network: {status['prediction_service']['neural_network']['status']}")
            print(f"   Database: {status['prediction_service']['database']['status']}")
            print(f"   Autonomous Scanner: {'running' if status['autonomous_scanner']['is_running'] else 'stopped'}")
            print(f"   Total Predictions: {status['prediction_service']['database']['total_predictions']}")
            print(f"   Recent Predictions: {status['prediction_service']['database']['recent_predictions']}")
            
        elif args.backtest:
            # Backtesting mode
            results = system.run_backtesting()
            print(f"\n🔍 Backtesting Results:")
            print(f"   Status: {results['status']}")
            if results['status'] == 'completed':
                print(f"   Predictions Evaluated: {results['predictions_evaluated']}")
                if results['predictions_evaluated'] > 0:
                    print(f"   Average Accuracy: {results['average_accuracy']:.3f}")

        elif args.dashboard:
            # Prediction Dashboard mode
            print("\n🔍 StockTrek Prediction Anomaly Dashboard")
            print("   📊 Analyzing prediction quality and anomalies")
            print("   🤖 AI-powered anomaly detection and review")
            print("   💡 Improvement recommendations\n")

            try:
                from ETL.prediction_dashboard import PredictionDashboard
                dashboard = PredictionDashboard()
                dashboard.print_dashboard_report()
            except Exception as e:
                print(f"❌ Dashboard error: {e}")

        elif args.daemon:
            # Daemon control mode
            print(f"\n🔧 StockTrek Daemon Control: {args.daemon}")
            try:
                import subprocess
                result = subprocess.run([
                    sys.executable, 'scripts/daemon_service.py', args.daemon
                ], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Daemon control error: {e}")
            except FileNotFoundError:
                print("❌ Daemon controller not found. Make sure stocktrek_daemon.py exists.")

        elif args.monitor:
            # Database monitoring mode
            print("\n📊 StockTrek Database Monitor")
            print("   📈 Real-time database statistics")
            print("   🔍 Prediction analysis and performance metrics")
            print("   📋 Recent activity and accuracy tracking\n")

            try:
                import subprocess
                subprocess.run([sys.executable, 'scripts/db_monitor.py'], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Monitor error: {e}")
            except FileNotFoundError:
                print("❌ Database monitor not found. Make sure db_monitor.py exists.")

        elif args.live:
            # Live monitoring mode
            print("\n🔴 StockTrek Live Database Monitor")
            print("   📊 Real-time updates every 30 seconds")
            print("   🔄 Continuous monitoring of predictions and accuracy")
            print("   ⏹️  Press Ctrl+C to stop\n")

            try:
                import subprocess
                subprocess.run([sys.executable, 'scripts/db_monitor.py', '--live'], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Live monitor error: {e}")
            except FileNotFoundError:
                print("❌ Database monitor not found. Make sure db_monitor.py exists.")

        elif args.export:
            # Data export mode
            print(f"\n📊 StockTrek Data Export: {args.export}")
            print("   📦 Generating investor-ready reports")
            print("   📋 Complete audit trail and verification data")
            print("   💼 Professional presentation package\n")

            try:
                import subprocess
                cmd = [sys.executable, 'export_data.py']

                if args.export == 'complete':
                    cmd.extend(['--complete', '--days', str(args.export_days)])
                elif args.export == 'quick':
                    cmd.extend(['--quick', '--days', str(args.export_days)])
                elif args.export == 'investor':
                    cmd.append('--investor')

                subprocess.run(cmd, check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Export error: {e}")
            except FileNotFoundError:
                print("❌ Export tool not found. Make sure export_data.py exists.")

        elif args.schedule_reports:
            # Automated report scheduler
            print("\n📅 StockTrek Automated Report Scheduler")
            print("   📊 Daily, weekly, and monthly reports")
            print("   📋 Investor-ready automated exports")
            print("   🔄 Continuous data collection verification\n")

            try:
                from ETL.report_scheduler import ReportScheduler
                scheduler = ReportScheduler()
                scheduler.run_scheduler()
            except KeyboardInterrupt:
                print("\n⏹️ Report scheduler stopped")
            except Exception as e:
                print(f"❌ Scheduler error: {e}")

        elif args.hft:
            # High-Frequency Trading mode
            print("\n⚡ Starting StockTrek in High-Frequency Trading Mode")
            print("   🚀 Ultra-fast 5-minute prediction cycles")
            print("   📈 Rapid self-improvement through reward/punishment")
            print("   📰 24-hour sentiment refresh cycles")
            print("   ⏹️  Press Ctrl+C to stop\n")
            system.start_hft_mode()

        elif args.slow:
            # Slow daemon mode
            print("\n🐌 Starting StockTrek in Slow Daemon Mode")
            print("   📊 Longer prediction intervals for stability")
            print("   🔄 Conservative learning approach")
            print("   ⏹️  Press Ctrl+C to stop\n")
            system.start_slow_mode()

        else:
            # Autonomous mode (default)
            mode_desc = "Parallel Training" if args.parallel else "Standard Autonomous"
            print(f"\n🤖 Starting StockTrek in {mode_desc} Mode")
            print("   📊 Will scan markets and make predictions for multiple timeframes")
            print("   🔄 Automatic backtesting when prediction timeframes are met")
            if args.parallel:
                print("   🔗 Parallel training enabled - shares learning with other instances")
            print("   ⏹️  Press Ctrl+C to stop\n")
            system.start_autonomous_mode(parallel_mode=args.parallel)
            
    except KeyboardInterrupt:
        logger.info("🛑 Interrupted by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
