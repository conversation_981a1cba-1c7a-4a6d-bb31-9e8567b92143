#!/usr/bin/env python3
"""
Autonomous Training Daemon for StockTrek
Manages autonomous training until all stocks are analyzed and backtested
Coordinates between HFT and daily modes with progress tracking
"""

import os
import sys
import time
import signal
import logging
import threading
import schedule
from datetime import datetime, timedelta
from pathlib import Path
import psutil

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from ETL.autonomous_training_manager import AutonomousTrainingManager
from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/stocktrek_autonomous_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutonomousTrainingDaemon:
    """
    Daemon that manages autonomous training system
    Coordinates HFT and daily modes until all stocks are processed
    """
    
    def __init__(self):
        """Initialize the autonomous training daemon"""
        self.training_manager = AutonomousTrainingManager()
        self.db_service = DatabaseService()
        
        # Daemon state
        self.is_running = False
        self.shutdown_requested = False
        
        # Progress tracking
        self.last_progress_report = None
        self.training_start_time = None
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        logger.info("🤖 Autonomous Training Daemon initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
        self.shutdown_requested = True
        self.stop()
    
    def start(self):
        """Start the autonomous training daemon"""
        try:
            if self.is_running:
                logger.warning("🤖 Daemon already running")
                return False
            
            logger.info("🚀 Starting Autonomous Training Daemon...")
            self.is_running = True
            self.training_start_time = datetime.now()
            
            # Schedule daily stock list updates
            schedule.every().day.at("17:00").do(self._update_stock_list)
            
            # Schedule daily training reset
            schedule.every().day.at("06:00").do(self._reset_daily_training)
            
            # Start autonomous training
            if not self.training_manager.start_autonomous_training():
                logger.error("❌ Failed to start autonomous training")
                return False
            
            # Start main daemon loop
            self._run_daemon_loop()
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting daemon: {e}")
            return False
    
    def stop(self):
        """Stop the autonomous training daemon"""
        try:
            logger.info("🛑 Stopping Autonomous Training Daemon...")
            
            self.is_running = False
            
            # Stop autonomous training
            self.training_manager.stop_autonomous_training()
            
            # Clear scheduled jobs
            schedule.clear()
            
            logger.info("🛑 Autonomous Training Daemon stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping daemon: {e}")
            return False
    
    def _run_daemon_loop(self):
        """Main daemon loop"""
        logger.info("🔄 Starting daemon main loop...")
        
        while self.is_running and not self.shutdown_requested:
            try:
                # Run scheduled tasks
                schedule.run_pending()
                
                # Check training progress
                self._check_training_progress()
                
                # Check if training cycle is complete
                if self._is_training_cycle_complete():
                    logger.info("✅ Training cycle completed for today")
                    self._handle_training_completion()
                
                # Sleep for a short interval
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in daemon loop: {e}")
                time.sleep(60)  # Wait longer on error
        
        logger.info("🔄 Daemon main loop stopped")
    
    def _check_training_progress(self):
        """Check and report training progress"""
        try:
            progress = self.training_manager.get_training_progress()
            
            # Report progress every 5 minutes
            now = datetime.now()
            if (not self.last_progress_report or 
                (now - self.last_progress_report).total_seconds() >= 300):
                
                self._report_progress(progress)
                self.last_progress_report = now
            
            # Check for stalled training
            if self._is_training_stalled(progress):
                logger.warning("⚠️ Training appears stalled, attempting restart...")
                self._restart_training()
            
        except Exception as e:
            logger.error(f"Error checking training progress: {e}")
    
    def _report_progress(self, progress: dict):
        """Report current training progress"""
        try:
            total = progress.get('total_stocks', 0)
            completed = progress.get('completed_stocks', 0)
            hft_processed = progress.get('hft_stocks_processed', 0)
            daily_processed = progress.get('daily_stocks_processed', 0)
            progress_percent = progress.get('progress_percent', 0)
            
            market_status = "🟢 OPEN" if progress.get('market_open', False) else "🔴 CLOSED"
            hft_status = "⚡ ACTIVE" if progress.get('hft_mode_active', False) else "⚡ INACTIVE"
            daily_status = "📅 ACTIVE" if progress.get('daily_mode_active', False) else "📅 INACTIVE"
            
            # Calculate time elapsed
            elapsed = datetime.now() - self.training_start_time if self.training_start_time else timedelta(0)
            elapsed_str = str(elapsed).split('.')[0]  # Remove microseconds
            
            logger.info(f"""
📊 AUTONOMOUS TRAINING PROGRESS REPORT
═══════════════════════════════════════
📈 Progress: {progress_percent:.1f}% ({completed}/{total} stocks)
⚡ HFT Processed: {hft_processed}
📅 Daily Processed: {daily_processed}
🏪 Market: {market_status}
⚡ HFT Mode: {hft_status}
📅 Daily Mode: {daily_status}
⏱️ Elapsed: {elapsed_str}
═══════════════════════════════════════
            """)
            
        except Exception as e:
            logger.error(f"Error reporting progress: {e}")
    
    def _is_training_stalled(self, progress: dict) -> bool:
        """Check if training appears to be stalled"""
        try:
            # Check if both modes are inactive during market hours
            market_open = progress.get('market_open', False)
            hft_active = progress.get('hft_mode_active', False)
            daily_active = progress.get('daily_mode_active', False)
            
            # If market is open but HFT is not active, might be stalled
            if market_open and not hft_active:
                return True
            
            # If market is closed but daily mode is not active and not completed
            if not market_open and not daily_active and not progress.get('training_cycle_completed', False):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if training stalled: {e}")
            return False
    
    def _restart_training(self):
        """Restart training if it appears stalled"""
        try:
            logger.info("🔄 Restarting autonomous training...")
            
            # Stop current training
            self.training_manager.stop_autonomous_training()
            time.sleep(5)
            
            # Start training again
            self.training_manager.start_autonomous_training()
            
            logger.info("✅ Training restart completed")
            
        except Exception as e:
            logger.error(f"Error restarting training: {e}")
    
    def _is_training_cycle_complete(self) -> bool:
        """Check if training cycle is complete for today"""
        try:
            progress = self.training_manager.get_training_progress()
            return progress.get('training_cycle_completed', False)
            
        except Exception as e:
            logger.error(f"Error checking training completion: {e}")
            return False
    
    def _handle_training_completion(self):
        """Handle completion of training cycle"""
        try:
            logger.info("🎉 Training cycle completed!")
            
            # Generate completion report
            self._generate_completion_report()
            
            # Wait until next day to start new cycle
            logger.info("😴 Waiting for next training cycle...")
            
        except Exception as e:
            logger.error(f"Error handling training completion: {e}")
    
    def _generate_completion_report(self):
        """Generate training completion report"""
        try:
            progress = self.training_manager.get_training_progress()
            
            total_time = datetime.now() - self.training_start_time if self.training_start_time else timedelta(0)
            
            report = f"""
🎉 TRAINING CYCLE COMPLETION REPORT
═══════════════════════════════════════
📅 Date: {datetime.now().strftime('%Y-%m-%d')}
📈 Total Stocks: {progress.get('total_stocks', 0)}
⚡ HFT Processed: {progress.get('hft_stocks_processed', 0)}
📅 Daily Processed: {progress.get('daily_stocks_processed', 0)}
✅ Completed: {progress.get('completed_stocks', 0)}
⏱️ Total Time: {str(total_time).split('.')[0]}
═══════════════════════════════════════
            """
            
            logger.info(report)
            
            # Save report to file
            report_file = f"/tmp/stocktrek_training_report_{datetime.now().strftime('%Y%m%d')}.txt"
            with open(report_file, 'w') as f:
                f.write(report)
            
            logger.info(f"📄 Report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"Error generating completion report: {e}")
    
    def _update_stock_list(self):
        """Scheduled stock list update"""
        try:
            logger.info("📊 Performing scheduled stock list update...")
            self.training_manager.update_stock_list()
            logger.info("✅ Stock list update completed")
            
        except Exception as e:
            logger.error(f"Error in scheduled stock list update: {e}")
    
    def _reset_daily_training(self):
        """Scheduled daily training reset"""
        try:
            logger.info("🔄 Performing scheduled daily training reset...")
            self.training_manager.reset_daily_training()
            self.training_start_time = datetime.now()  # Reset timer
            logger.info("✅ Daily training reset completed")
            
        except Exception as e:
            logger.error(f"Error in scheduled daily training reset: {e}")
    
    def get_status(self) -> dict:
        """Get daemon status"""
        try:
            progress = self.training_manager.get_training_progress()
            
            return {
                'daemon_running': self.is_running,
                'training_progress': progress,
                'uptime': str(datetime.now() - self.training_start_time).split('.')[0] if self.training_start_time else "0:00:00",
                'last_progress_report': self.last_progress_report.isoformat() if self.last_progress_report else None
            }
            
        except Exception as e:
            logger.error(f"Error getting daemon status: {e}")
            return {'error': str(e)}

def main():
    """Main entry point"""
    daemon = AutonomousTrainingDaemon()
    
    try:
        daemon.start()
    except KeyboardInterrupt:
        logger.info("🛑 Received keyboard interrupt")
    finally:
        daemon.stop()

if __name__ == "__main__":
    main()
