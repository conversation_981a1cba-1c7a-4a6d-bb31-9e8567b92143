#!/usr/bin/env python3
"""
Quick StockTrek system status check
"""

import sys
import os
# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from ETL.prediction_service import PredictionService
from Databases.database_service import DatabaseService
from datetime import datetime, timed<PERSON><PERSON>

def check_system_status():
    """Check complete system status"""
    print("🔍 StockTrek System Status Check")
    print("=" * 50)
    
    try:
        # Check database
        print("\n📊 Database Status:")
        db = DatabaseService()
        conn = db.get_connection()
        if conn:
            print("   ✅ Database: Connected")
            
            # Check recent predictions
            query = """
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN prediction_date >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent
            FROM neural_network_predictions
            """
            result = db.execute_query(query, fetch=True)
            if result:
                total = result[0]['total']
                recent = result[0]['recent']
                print(f"   📈 Total predictions: {total}")
                print(f"   ⚡ Last hour: {recent}")
            
            # Check companies
            query2 = "SELECT COUNT(*) as count FROM companies"
            result2 = db.execute_query(query2, fetch=True)
            if result2:
                companies = result2[0]['count']
                print(f"   🏢 Companies tracked: {companies}")
        else:
            print("   ❌ Database: Disconnected")
            
        # Check prediction service
        print("\n🧠 Neural Network Status:")
        service = PredictionService()
        status = service.get_system_status()
        
        nn_status = status.get('neural_network', {})
        print(f"   🤖 Model loaded: {nn_status.get('model_loaded', False)}")
        print(f"   📊 Features: {nn_status.get('feature_count', 0)}")
        print(f"   🔢 Version: {nn_status.get('model_version', 'Unknown')}")
        
        # Check data export folder
        print("\n📁 Data Export Status:")
        desktop_path = os.path.expanduser("~/Desktop/StockTrek Data")
        if os.path.exists(desktop_path):
            print(f"   ✅ Export folder exists: {desktop_path}")
            files = os.listdir(desktop_path)
            csv_files = [f for f in files if f.endswith('.csv')]
            json_files = [f for f in files if f.endswith('.json')]
            print(f"   📄 CSV files: {len(csv_files)}")
            print(f"   📄 JSON files: {len(json_files)}")
            
            # Check latest export
            if csv_files:
                latest_csv = max(csv_files, key=lambda x: os.path.getctime(os.path.join(desktop_path, x)))
                mod_time = datetime.fromtimestamp(os.path.getctime(os.path.join(desktop_path, latest_csv)))
                print(f"   🕐 Latest export: {latest_csv} ({mod_time.strftime('%Y-%m-%d %H:%M')})")
        else:
            print(f"   ❌ Export folder missing: {desktop_path}")
            
        # Test live data quality
        print("\n🔍 Data Quality Test:")
        test_symbols = ['AAPL', 'MSFT', 'GOOGL']
        
        for symbol in test_symbols:
            try:
                company_data = service.process_company_data(symbol)
                current_price = company_data.get('current_price')
                data_quality = company_data.get('data_quality', 'unknown')
                
                if current_price and current_price > 0:
                    print(f"   ✅ {symbol}: ${current_price:.2f} ({data_quality})")
                else:
                    print(f"   ❌ {symbol}: No valid price data")
                    
            except Exception as e:
                print(f"   ❌ {symbol}: Error - {e}")
                
        print("\n🎯 System Status: Ready for Production")
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ System check failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_system_status()
