#!/bin/bash
# StockTrek Daemon Setup Script

echo "🚀 Setting up StockTrek 24/7 Daemon System"
echo "=========================================="

# Make scripts executable
chmod +x stocktrek_daemon.py
chmod +x daemon_service.py
chmod +x db_monitor.py
chmod +x main.py

# Create necessary directories
mkdir -p logs
mkdir -p config

# Create default daemon config if it doesn't exist
if [ ! -f "config/daemon_config.json" ]; then
    echo "📝 Creating default daemon configuration..."
    cat > config/daemon_config.json << 'EOF'
{
  "autonomous_scan_interval": 300,
  "prediction_check_interval": 3600,
  "health_check_interval": 60,
  "max_retries": 3,
  "retry_delay": 30,
  "log_level": "INFO",
  "enable_autonomous_scanning": true,
  "enable_prediction_evaluation": true,
  "enable_auto_learning": true,
  "hft_mode": {
    "enabled": true,
    "prediction_interval": 300,
    "sentiment_refresh_interval": 86400,
    "rapid_learning": true,
    "max_predictions_per_hour": 12,
    "confidence_threshold": 60.0
  },
  "training": {
    "continuous_learning": true,
    "reward_multiplier": 2.0,
    "punishment_multiplier": 1.5,
    "accuracy_target": 0.75,
    "retrain_threshold": 100
  },
  "market_hours": {
    "start": "09:30",
    "end": "16:00",
    "timezone": "US/Eastern"
  }
}
EOF
fi

echo "✅ Setup complete!"
echo ""
echo "🎯 Quick Start Commands:"
echo "========================"
echo "Start 24/7 daemon:     python3 main.py --daemon start"
echo "Check daemon status:   python3 main.py --daemon status"
echo "Monitor database:      python3 main.py --monitor"
echo "Live monitoring:       python3 main.py --live"
echo "Stop daemon:           python3 main.py --daemon stop"
echo ""
echo "📊 Database Monitoring:"
echo "======================="
echo "View dashboard:        python3 db_monitor.py"
echo "Show statistics:       python3 db_monitor.py --stats"
echo "Recent predictions:    python3 db_monitor.py --predictions"
echo "Accuracy report:       python3 db_monitor.py --accuracy"
echo "HFT performance:       python3 db_monitor.py --hft"
echo "Live monitoring:       python3 db_monitor.py --live"
echo ""
echo "⚡ HFT Training Mode:"
echo "===================="
echo "The daemon automatically runs HFT training with:"
echo "• 5-minute prediction cycles"
echo "• 24-hour sentiment refresh"
echo "• Rapid reward/punishment learning"
echo "• Continuous database updates"
echo ""
echo "🔧 Configuration:"
echo "================="
echo "Edit config/daemon_config.json to customize:"
echo "• Prediction intervals"
echo "• Learning parameters"
echo "• HFT settings"
echo "• Market hours"
echo ""
echo "📋 Logs:"
echo "========"
echo "Daemon logs: logs/daemon.log"
echo "Main logs:   stocktrek.log"
echo ""
echo "🚀 Ready to start your 24/7 autonomous stock prediction system!"
