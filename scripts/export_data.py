#!/usr/bin/env python3
"""
StockTrek Data Export Tool
Command-line interface for exporting all system data for investor presentations
"""

import sys
import argparse
import logging
from datetime import datetime
from pathlib import Path

from ETL.data_export_service import DataExportService

def setup_logging():
    """Setup logging for export operations"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/data_export.log', mode='a')
        ]
    )

def export_complete_data(days_back: int = 30):
    """Export complete system data"""
    print("\n🚀 StockTrek Complete Data Export")
    print("=" * 60)
    print(f"📅 Exporting data from last {days_back} days")
    print("📊 Generating investor-ready reports...")
    print()
    
    try:
        export_service = DataExportService()
        zip_path = export_service.export_all_data(days_back)
        
        print("✅ EXPORT COMPLETE!")
        print("=" * 60)
        print(f"📦 Archive created: {zip_path}")
        print(f"📁 Size: {Path(zip_path).stat().st_size / 1024 / 1024:.1f} MB")
        print()
        print("📋 Export includes:")
        print("   • Complete predictions database (CSV, JSON, Excel)")
        print("   • Performance metrics and accuracy reports")
        print("   • HFT training data and results")
        print("   • Learning feedback and model improvement data")
        print("   • System logs and daemon activity")
        print("   • Executive summary for investors")
        print("   • Company data and market analysis")
        print()
        print("🎯 Ready for investor presentation!")
        
        return zip_path
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
        logging.error(f"Export failed: {e}")
        return None

def export_quick_report(days_back: int = 7):
    """Export quick performance report"""
    print(f"\n📊 StockTrek Quick Report ({days_back} days)")
    print("=" * 50)
    
    try:
        export_service = DataExportService()
        
        # Create quick export directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        quick_dir = Path("exports") / f"quick_report_{timestamp}"
        quick_dir.mkdir(parents=True, exist_ok=True)
        
        # Export key data
        export_service.export_predictions_data(quick_dir, days_back)
        export_service.export_performance_metrics(quick_dir, days_back)
        export_service.generate_summary_report(quick_dir, days_back)
        
        print(f"✅ Quick report generated: {quick_dir}")
        print("📋 Includes: predictions, performance, summary")
        
        return str(quick_dir)
        
    except Exception as e:
        print(f"❌ Quick report failed: {e}")
        return None

def show_system_status():
    """Show current system status"""
    print("\n🔍 StockTrek System Status")
    print("=" * 40)
    
    try:
        export_service = DataExportService()
        
        # Get status data
        daemon_status = export_service.get_daemon_status()
        system_health = export_service.get_system_health()
        prediction_summary = export_service.get_prediction_summary(7)
        hft_performance = export_service.get_hft_performance(7)
        
        # Display status
        print(f"Daemon Status: {'🟢 RUNNING' if daemon_status.get('status') == 'running' else '🔴 STOPPED'}")
        if daemon_status.get('status') == 'running':
            print(f"   PID: {daemon_status.get('pid')}")
            print(f"   CPU: {daemon_status.get('cpu_percent', 0):.1f}%")
            print(f"   Memory: {daemon_status.get('memory_mb', 0):.1f} MB")
        
        print(f"Database: {'🟢 HEALTHY' if system_health.get('database_healthy') else '🔴 UNHEALTHY'}")
        print(f"Recent Activity: {system_health.get('recent_predictions_24h', 0)} predictions (24h)")
        print()
        
        print("📊 PERFORMANCE (Last 7 days)")
        print("-" * 30)
        print(f"Total Predictions: {prediction_summary.get('total_predictions', 0):,}")
        print(f"Accuracy Rate: {prediction_summary.get('accuracy_rate', 0):.1%}")
        print(f"Average Confidence: {prediction_summary.get('avg_confidence', 0):.1f}%")
        print()
        
        print("⚡ HFT PERFORMANCE (Last 7 days)")
        print("-" * 30)
        print(f"HFT Predictions: {hft_performance.get('hft_total_predictions', 0):,}")
        print(f"HFT Accuracy: {hft_performance.get('hft_accuracy_rate', 0):.1%}")
        print(f"HFT Avg Confidence: {hft_performance.get('hft_avg_confidence', 0):.1f}%")
        
    except Exception as e:
        print(f"❌ Status check failed: {e}")

def export_investor_package():
    """Create comprehensive investor package"""
    print("\n💼 Creating Investor Package")
    print("=" * 50)
    print("📦 This will create a comprehensive package including:")
    print("   • 30-day complete data export")
    print("   • Executive summary report")
    print("   • Performance verification data")
    print("   • System operational proof")
    print("   • All logs and audit trails")
    print()
    
    confirm = input("Continue? (y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ Cancelled")
        return
    
    # Export 30 days of data
    zip_path = export_complete_data(30)
    
    if zip_path:
        print()
        print("💼 INVESTOR PACKAGE READY!")
        print("=" * 50)
        print(f"📦 Package: {zip_path}")
        print("📋 This package contains complete verification data")
        print("    suitable for investor presentations and due diligence.")
        print()
        print("🎯 Key highlights to mention to investors:")
        print("   • Autonomous 24/7 operation with proof of activity")
        print("   • High-frequency trading capabilities")
        print("   • Machine learning with continuous improvement")
        print("   • Complete audit trail and performance metrics")
        print("   • Real-time database monitoring and reporting")

def main():
    """Main entry point"""
    setup_logging()
    
    parser = argparse.ArgumentParser(
        description="StockTrek Data Export Tool - Generate investor-ready reports",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 export_data.py --complete                    # Full 30-day export
  python3 export_data.py --complete --days 60          # Full 60-day export
  python3 export_data.py --quick                       # Quick 7-day report
  python3 export_data.py --quick --days 14             # Quick 14-day report
  python3 export_data.py --status                      # System status
  python3 export_data.py --investor                    # Investor package
        """
    )
    
    parser.add_argument('--complete', action='store_true',
                       help='Export complete system data (default: 30 days)')
    parser.add_argument('--quick', action='store_true',
                       help='Export quick performance report (default: 7 days)')
    parser.add_argument('--status', action='store_true',
                       help='Show current system status')
    parser.add_argument('--investor', action='store_true',
                       help='Create comprehensive investor package')
    parser.add_argument('--days', type=int, default=None,
                       help='Number of days to include (default: 30 for complete, 7 for quick)')
    
    args = parser.parse_args()
    
    if args.investor:
        export_investor_package()
    elif args.complete:
        days = args.days or 30
        export_complete_data(days)
    elif args.quick:
        days = args.days or 7
        export_quick_report(days)
    elif args.status:
        show_system_status()
    else:
        # Default: show status and offer options
        show_system_status()
        print()
        print("🔧 EXPORT OPTIONS")
        print("=" * 30)
        print("1. Complete data export:  python3 export_data.py --complete")
        print("2. Quick report:          python3 export_data.py --quick")
        print("3. Investor package:      python3 export_data.py --investor")
        print("4. System status:         python3 export_data.py --status")

if __name__ == "__main__":
    main()
