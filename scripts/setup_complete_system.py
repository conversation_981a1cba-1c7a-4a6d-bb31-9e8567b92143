#!/usr/bin/env python3
"""
StockTrek Complete System Setup and Verification
Ensures everything works properly with automatic HFT training and data exports
"""

import os
import sys
import subprocess
import logging
import psycopg2
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StockTrekSetup:
    """Complete system setup and verification"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.db_config = {
            'database': 'stocktrek',
            'user': 'stocktrek_admin', 
            'password': 'equity_FR',
            'host': 'localhost',
            'port': '5432'
        }
        
    def check_postgresql(self):
        """Check PostgreSQL installation and setup"""
        logger.info("🔍 Checking PostgreSQL setup...")
        
        try:
            # Test connection
            conn = psycopg2.connect(**self.db_config)
            conn.close()
            logger.info("✅ PostgreSQL connection successful")
            
            # Print access instructions
            print("\n📊 PostgreSQL Database Access:")
            print("=" * 50)
            print(f"Database: {self.db_config['database']}")
            print(f"User: {self.db_config['user']}")
            print(f"Host: {self.db_config['host']}")
            print(f"Port: {self.db_config['port']}")
            print("\n🔑 Access via terminal:")
            print(f"psql -h {self.db_config['host']} -p {self.db_config['port']} -U {self.db_config['user']} -d {self.db_config['database']}")
            print("\n📋 Common commands:")
            print("\\dt                    # List all tables")
            print("\\d predictions        # Describe predictions table")
            print("SELECT * FROM predictions LIMIT 10;  # View recent predictions")
            print("SELECT COUNT(*) FROM predictions;    # Count total predictions")
            print("\\q                    # Quit psql")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ PostgreSQL connection failed: {e}")
            print("\n🔧 PostgreSQL Setup Required:")
            print("1. Install PostgreSQL: brew install postgresql")
            print("2. Start service: brew services start postgresql")
            print("3. Create database and user:")
            print("   createdb stocktrek")
            print("   psql stocktrek")
            print("   CREATE USER stocktrek_admin WITH PASSWORD 'equity_FR';")
            print("   GRANT ALL PRIVILEGES ON DATABASE stocktrek TO stocktrek_admin;")
            return False
    
    def check_dependencies(self):
        """Check all required dependencies"""
        logger.info("📦 Checking dependencies...")
        
        required_packages = [
            'psycopg2-binary', 'pandas', 'numpy', 'scikit-learn',
            'yfinance', 'requests', 'schedule', 'psutil'
        ]
        
        missing = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                logger.info(f"✅ {package}")
            except ImportError:
                missing.append(package)
                logger.warning(f"❌ {package}")
        
        if missing:
            logger.info(f"Installing missing packages: {missing}")
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing)
        
        return len(missing) == 0
    
    def setup_database_tables(self):
        """Setup database tables"""
        logger.info("🗄️ Setting up database tables...")
        
        try:
            from Databases.database_service import DatabaseService
            db = DatabaseService()
            
            # Read and execute SQL setup
            sql_file = self.project_root / "Databases" / "database.sql"
            if sql_file.exists():
                with open(sql_file, 'r') as f:
                    sql_content = f.read()
                
                # Execute SQL setup
                conn = db.get_connection()
                if conn:
                    with conn.cursor() as cur:
                        cur.execute(sql_content)
                    logger.info("✅ Database tables created/updated")
                    return True
            else:
                logger.warning("❌ database.sql not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Database setup failed: {e}")
            return False
    
    def create_directories(self):
        """Create required directories"""
        logger.info("📁 Creating directories...")
        
        directories = [
            "logs", "config", "exports", "exports/scheduled",
            "exports/scheduled/daily", "exports/scheduled/weekly", 
            "exports/scheduled/monthly"
        ]
        
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(exist_ok=True)
            logger.info(f"✅ {dir_name}")
    
    def create_desktop_folder(self):
        """Create StockTrek Data folder on desktop"""
        logger.info("🖥️ Creating desktop export folder...")
        
        desktop_path = Path.home() / "Desktop" / "StockTrek Data"
        desktop_path.mkdir(exist_ok=True)
        logger.info(f"✅ Created: {desktop_path}")
    
    def test_neural_network(self):
        """Test neural network prediction"""
        logger.info("🧠 Testing neural network...")
        
        try:
            from Neural_Network.neural_network_service import NeuralNetworkService
            nn = NeuralNetworkService()
            
            # Test prediction
            test_data = {
                'current_price': 150.0,
                'volume': 1000000,
                'market_cap': 1000000000,
                'pe_ratio': 15.0
            }
            
            prediction = nn.predict_stock(test_data)
            if prediction and prediction.get('predicted_price', 0) != 2.0:
                logger.info(f"✅ Neural network working - predicted: ${prediction.get('predicted_price', 'N/A')}")
                return True
            else:
                logger.warning("❌ Neural network returning $2.0 - needs fixing")
                return False
                
        except Exception as e:
            logger.error(f"❌ Neural network test failed: {e}")
            return False
    
    def test_system_integration(self):
        """Test complete system integration"""
        logger.info("🔧 Testing system integration...")
        
        try:
            # Test main.py help
            result = subprocess.run([sys.executable, "main.py", "--help"], 
                                  capture_output=True, text=True, cwd=self.project_root)
            if result.returncode == 0:
                logger.info("✅ Main.py working")
            else:
                logger.error("❌ Main.py failed")
                return False
            
            # Test daemon service
            daemon_file = self.project_root / "daemon_service.py"
            if daemon_file.exists():
                logger.info("✅ Daemon service available")
            else:
                logger.error("❌ Daemon service missing")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ System integration test failed: {e}")
            return False
    
    def run_complete_setup(self):
        """Run complete system setup"""
        print("🚀 StockTrek Complete System Setup")
        print("=" * 50)
        
        success = True
        
        # Check dependencies
        if not self.check_dependencies():
            logger.warning("⚠️ Some dependencies missing but continuing...")
        
        # Create directories
        self.create_directories()
        
        # Create desktop folder
        self.create_desktop_folder()
        
        # Check PostgreSQL
        if not self.check_postgresql():
            success = False
        
        # Setup database tables
        if not self.setup_database_tables():
            success = False
        
        # Test neural network
        if not self.test_neural_network():
            logger.warning("⚠️ Neural network needs attention")
        
        # Test system integration
        if not self.test_system_integration():
            success = False
        
        print("\n" + "=" * 50)
        if success:
            print("✅ SYSTEM SETUP COMPLETE!")
            print("\n🚀 Ready to start:")
            print("python3 daemon_service.py start    # Start daemon with auto HFT training")
            print("python3 main.py --hft              # Manual HFT mode")
            print("python3 main.py --export investor  # Generate investor report")
            print("\n📊 Data will be exported to ~/Desktop/StockTrek Data every 4 hours")
        else:
            print("❌ SETUP INCOMPLETE - Please fix the issues above")
        
        return success

def main():
    """Main setup function"""
    setup = StockTrekSetup()
    setup.run_complete_setup()

if __name__ == "__main__":
    main()
