#!/usr/bin/env python3
"""
Test script for the autonomous training system
Allows testing of scheduled/automated functionality immediately
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from ETL.autonomous_training_manager import AutonomousTrainingManager
from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_stock_list_management():
    """Test stock list management functionality"""
    logger.info("🧪 Testing stock list management...")
    
    manager = AutonomousTrainingManager()
    
    # Test stock list update
    result = manager.update_stock_list()
    if result:
        logger.info("✅ Stock list update successful")
    else:
        logger.error("❌ Stock list update failed")
    
    # Get current stock count
    progress = manager.get_training_progress()
    logger.info(f"📊 Current stock count: {progress.get('total_stocks', 0)}")
    
    return result

def test_hft_prediction():
    """Test HFT prediction functionality"""
    logger.info("🧪 Testing HFT prediction...")
    
    manager = AutonomousTrainingManager()
    
    # Test HFT prediction on a sample stock
    test_ticker = "AAPL"
    result = manager.process_hft_prediction(test_ticker)
    
    if result:
        logger.info(f"✅ HFT prediction for {test_ticker} successful")
    else:
        logger.error(f"❌ HFT prediction for {test_ticker} failed")
    
    return result

def test_daily_prediction():
    """Test daily prediction functionality"""
    logger.info("🧪 Testing daily prediction...")
    
    manager = AutonomousTrainingManager()
    
    # Test daily prediction on a sample stock
    test_ticker = "MSFT"
    result = manager.process_daily_prediction(test_ticker)
    
    if result:
        logger.info(f"✅ Daily prediction for {test_ticker} successful")
    else:
        logger.error(f"❌ Daily prediction for {test_ticker} failed")
    
    return result

def test_training_progress():
    """Test training progress tracking"""
    logger.info("🧪 Testing training progress tracking...")
    
    manager = AutonomousTrainingManager()
    
    progress = manager.get_training_progress()
    
    if progress:
        logger.info("✅ Training progress retrieval successful")
        logger.info(f"📊 Progress: {progress.get('progress_percent', 0):.1f}%")
        logger.info(f"📈 Total stocks: {progress.get('total_stocks', 0)}")
        logger.info(f"⚡ HFT processed: {progress.get('hft_stocks_processed', 0)}")
        logger.info(f"📅 Daily processed: {progress.get('daily_stocks_processed', 0)}")
        logger.info(f"🏪 Market open: {progress.get('market_open', False)}")
    else:
        logger.error("❌ Training progress retrieval failed")
    
    return bool(progress)

def test_market_hours_detection():
    """Test market hours detection"""
    logger.info("🧪 Testing market hours detection...")
    
    manager = AutonomousTrainingManager()
    
    is_open = manager.is_market_open()
    logger.info(f"🏪 Market is currently: {'OPEN' if is_open else 'CLOSED'}")
    
    # Test with specific times
    test_times = [
        (9, 30),   # Market open
        (16, 0),   # Market close
        (12, 0),   # Market middle
        (20, 0),   # After hours
    ]
    
    for hour, minute in test_times:
        test_time = datetime.now().replace(hour=hour, minute=minute)
        is_open_test = manager._is_market_open_at_time(test_time)
        logger.info(f"🕐 {hour:02d}:{minute:02d} - Market would be: {'OPEN' if is_open_test else 'CLOSED'}")
    
    return True

def test_sentiment_limiting():
    """Test sentiment analysis limiting (one per stock per day)"""
    logger.info("🧪 Testing sentiment limiting...")
    
    manager = AutonomousTrainingManager()
    
    test_ticker = "GOOGL"
    
    # First sentiment analysis should succeed
    can_analyze_1 = manager._can_analyze_sentiment(test_ticker)
    logger.info(f"🔍 First sentiment check for {test_ticker}: {'✅ ALLOWED' if can_analyze_1 else '❌ BLOCKED'}")
    
    if can_analyze_1:
        # Record sentiment analysis
        manager._record_sentiment_analysis(test_ticker)
        
        # Second attempt should be blocked
        can_analyze_2 = manager._can_analyze_sentiment(test_ticker)
        logger.info(f"🔍 Second sentiment check for {test_ticker}: {'✅ ALLOWED' if can_analyze_2 else '❌ BLOCKED'}")
        
        return not can_analyze_2  # Should be False (blocked)
    
    return can_analyze_1

def test_autonomous_training_cycle():
    """Test a complete autonomous training cycle"""
    logger.info("🧪 Testing autonomous training cycle...")
    
    manager = AutonomousTrainingManager()
    
    # Start autonomous training
    logger.info("🚀 Starting autonomous training...")
    if not manager.start_autonomous_training():
        logger.error("❌ Failed to start autonomous training")
        return False
    
    # Let it run for a short time
    logger.info("⏱️ Running for 30 seconds...")
    time.sleep(30)
    
    # Check progress
    progress = manager.get_training_progress()
    logger.info(f"📊 Progress after 30 seconds: {progress.get('progress_percent', 0):.1f}%")
    
    # Stop autonomous training
    logger.info("🛑 Stopping autonomous training...")
    if not manager.stop_autonomous_training():
        logger.error("❌ Failed to stop autonomous training")
        return False
    
    logger.info("✅ Autonomous training cycle test completed")
    return True

def test_training_reset():
    """Test training reset functionality"""
    logger.info("🧪 Testing training reset...")
    
    manager = AutonomousTrainingManager()
    
    # Reset training
    result = manager.reset_daily_training()
    
    if result:
        logger.info("✅ Training reset successful")
        
        # Check that progress is reset
        progress = manager.get_training_progress()
        logger.info(f"📊 Progress after reset: {progress.get('progress_percent', 0):.1f}%")
    else:
        logger.error("❌ Training reset failed")
    
    return result

def run_all_tests():
    """Run all autonomous training tests"""
    logger.info("🧪 Starting Autonomous Training System Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Stock List Management", test_stock_list_management),
        ("Market Hours Detection", test_market_hours_detection),
        ("Sentiment Limiting", test_sentiment_limiting),
        ("Training Progress", test_training_progress),
        ("HFT Prediction", test_hft_prediction),
        ("Daily Prediction", test_daily_prediction),
        ("Training Reset", test_training_reset),
        ("Autonomous Training Cycle", test_autonomous_training_cycle),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🔬 Running test: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"📋 Test result: {status}")
        except Exception as e:
            logger.error(f"💥 Test error: {e}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("🧪 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n📊 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 All tests passed! Autonomous training system is ready.")
    else:
        logger.warning("⚠️ Some tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"💥 Fatal error during testing: {e}")
        sys.exit(1)
