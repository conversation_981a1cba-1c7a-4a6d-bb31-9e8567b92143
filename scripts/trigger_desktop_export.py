#!/usr/bin/env python3
"""
Manually trigger desktop export to test daemon functionality
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from daemon_service import StockTrekDaemon

def test_daemon_export():
    """Test the daemon's export functionality directly"""
    print("🧪 Testing Daemon Desktop Export Function")
    print("=" * 50)
    
    try:
        # Create daemon instance (without starting full daemon)
        daemon = StockTrekDaemon()
        
        # Manually trigger desktop export
        daemon.export_to_desktop()
        
        print("✅ Desktop export function completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Desktop export test failed: {e}")
        return False

if __name__ == "__main__":
    test_daemon_export()
