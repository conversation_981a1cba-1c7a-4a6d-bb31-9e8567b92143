#!/bin/bash

# StockTrek Production Startup Script
# This starts the complete autonomous system with HFT training and data exports

echo "🚀 Starting StockTrek Production System"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "../main.py" ]; then
    echo "❌ Error: main.py not found. Please run from StockTrek directory."
    echo "💡 Try: cd ~/Desktop/StockTrek && ./scripts/start_stocktrek.sh"
    exit 1
fi

# Change to parent directory (StockTrek root)
cd ..

# Check Python version
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Error: Python3 not found. Please install Python 3.9+"
    exit 1
fi

# Check required packages
echo "🔍 Checking dependencies..."
python3 -c "import yfinance, psycopg2, sklearn, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Error: Missing required packages. Please install dependencies."
    echo "Run: pip3 install yfinance psycopg2-binary scikit-learn pandas numpy requests"
    exit 1
fi

# Check database connection
echo "🔍 Checking database connection..."
python3 -c "
import sys
sys.path.append('.')
from Databases.database_service import DatabaseService
db = DatabaseService()
conn = db.get_connection()
if conn:
    print('✅ Database connection successful')
else:
    print('❌ Database connection failed')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Database connection failed. Please check PostgreSQL is running."
    exit 1
fi

# Create StockTrek Data folder on desktop if it doesn't exist
DESKTOP_PATH="$HOME/Desktop"
STOCKTREK_DATA_PATH="$DESKTOP_PATH/StockTrek Data"

if [ ! -d "$STOCKTREK_DATA_PATH" ]; then
    echo "📁 Creating StockTrek Data folder on desktop..."
    mkdir -p "$STOCKTREK_DATA_PATH"
fi

echo "✅ StockTrek Data folder: $STOCKTREK_DATA_PATH"

# Start the autonomous daemon system
echo ""
echo "🤖 Starting StockTrek Autonomous Daemon..."
echo "Features enabled:"
echo "  ⚡ HFT Training (30-minute cycles)"
echo "  📊 Desktop Data Export (4-hour cycles)"
echo "  🛡️ No Dummy Data (Real market data only)"
echo "  🔄 24/7 Autonomous Operation"
echo "  📈 Continuous Learning & Backtesting"
echo ""
echo "Press Ctrl+C to stop the system"
echo "========================================"

# Run the HFT autonomous system
python3 main.py --hft

echo ""
echo "🛑 StockTrek system stopped"
