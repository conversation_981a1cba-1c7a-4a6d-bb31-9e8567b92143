#!/usr/bin/env python3
"""
StockTrek Database Monitor - Terminal-based database monitoring and analysis tool

Usage:
    python3 db_monitor.py                    # Interactive dashboard
    python3 db_monitor.py --stats            # Show database statistics
    python3 db_monitor.py --predictions      # Show recent predictions
    python3 db_monitor.py --accuracy         # Show accuracy metrics
    python3 db_monitor.py --hft              # Show HFT performance
    python3 db_monitor.py --live             # Live monitoring mode
"""

import sys
import os
import time
import argparse
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

# Add project root to path
sys.path.append(os.path.join(os.path.dirname(__file__)))

from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(level=logging.WARNING)  # Suppress info logs for cleaner output


class DatabaseMonitor:
    """Terminal-based database monitoring tool"""
    
    def __init__(self):
        self.db_service = DatabaseService()
        
    def get_database_stats(self) -> Dict:
        """Get comprehensive database statistics"""
        try:
            stats = {}
            
            # Basic counts
            queries = {
                'total_companies': "SELECT COUNT(*) FROM companies",
                'total_predictions': "SELECT COUNT(*) FROM neural_network_predictions",
                'total_company_data': "SELECT COUNT(*) FROM company_data",
                'recent_predictions_24h': """
                    SELECT COUNT(*) FROM neural_network_predictions 
                    WHERE prediction_date >= NOW() - INTERVAL '24 hours'
                """,
                'hft_predictions_24h': """
                    SELECT COUNT(*) FROM neural_network_predictions 
                    WHERE prediction_date >= NOW() - INTERVAL '24 hours' AND is_hft = true
                """,
                'evaluated_predictions': """
                    SELECT COUNT(*) FROM neural_network_predictions 
                    WHERE actual_outcome IS NOT NULL
                """,
                'accurate_predictions': """
                    SELECT COUNT(*) FROM neural_network_predictions 
                    WHERE prediction_accuracy = true
                """
            }
            
            for key, query in queries.items():
                try:
                    result = self.db_service.execute_query(query, fetch=True)
                    stats[key] = result[0][0] if result and len(result) > 0 else 0
                except Exception as e:
                    print(f"Warning: Query failed for {key}: {e}")
                    stats[key] = 0
            
            # Calculate accuracy rate
            if stats['evaluated_predictions'] > 0:
                stats['accuracy_rate'] = stats['accurate_predictions'] / stats['evaluated_predictions']
            else:
                stats['accuracy_rate'] = 0.0
            
            # Get latest prediction
            latest_query = """
                SELECT p.prediction_date, c.ticker, p.prediction_type, p.confidence_score
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                ORDER BY p.prediction_date DESC
                LIMIT 1
            """
            latest_result = self.db_service.execute_query(latest_query, fetch=True)
            if latest_result:
                stats['latest_prediction'] = {
                    'date': latest_result[0][0].strftime('%Y-%m-%d %H:%M:%S'),
                    'ticker': latest_result[0][1],
                    'prediction': latest_result[0][2],
                    'confidence': latest_result[0][3]
                }
            
            return stats
            
        except Exception as e:
            print(f"❌ Error getting database stats: {e}")
            return {}
    
    def get_recent_predictions(self, hours: int = 24, limit: int = 20) -> List[Dict]:
        """Get recent predictions"""
        try:
            query = """
                SELECT p.prediction_date, c.ticker, p.prediction_type, p.confidence_score,
                       p.current_price, p.predicted_price, p.price_change_percent,
                       p.timeframe_days, p.is_hft, p.prediction_accuracy,
                       p.prediction_metadata
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.prediction_date >= NOW() - INTERVAL %s
                ORDER BY p.prediction_date DESC
                LIMIT %s
            """
            
            results = self.db_service.execute_query(query, (f"{hours} hours", limit), fetch=True)
            
            predictions = []
            for row in results:
                pred = {
                    'date': row[0].strftime('%Y-%m-%d %H:%M:%S'),
                    'ticker': row[1],
                    'prediction': row[2],  # This is prediction_type from DB
                    'confidence': row[3],
                    'current_price': row[4],
                    'predicted_price': row[5],
                    'price_change_percent': row[6],
                    'timeframe_days': row[7],
                    'is_hft': row[8],
                    'accuracy': row[9],
                    'metadata': row[10]
                }
                predictions.append(pred)
            
            return predictions
            
        except Exception as e:
            print(f"❌ Error getting recent predictions: {e}")
            return []
    
    def get_accuracy_metrics(self, days: int = 7) -> Dict:
        """Get detailed accuracy metrics"""
        try:
            metrics = {}
            
            # Overall accuracy by timeframe
            timeframe_query = """
                SELECT timeframe_days,
                       COUNT(*) as total,
                       SUM(CASE WHEN prediction_accuracy THEN 1 ELSE 0 END) as correct,
                       AVG(CASE WHEN prediction_accuracy THEN 1.0 ELSE 0.0 END) as accuracy_rate
                FROM neural_network_predictions
                WHERE prediction_date >= NOW() - INTERVAL %s
                AND actual_outcome IS NOT NULL
                GROUP BY timeframe_days
                ORDER BY timeframe_days
            """

            timeframe_results = self.db_service.execute_query(timeframe_query, (f"{days} days",), fetch=True)
            metrics['by_timeframe'] = []
            for row in timeframe_results:
                metrics['by_timeframe'].append({
                    'timeframe_days': row[0],
                    'total_predictions': row[1],
                    'correct_predictions': row[2],
                    'accuracy_rate': float(row[3]) if row[3] else 0.0
                })
            
            # HFT vs Regular accuracy
            hft_query = """
                SELECT is_hft,
                       COUNT(*) as total,
                       SUM(CASE WHEN prediction_accuracy THEN 1 ELSE 0 END) as correct,
                       AVG(CASE WHEN prediction_accuracy THEN 1.0 ELSE 0.0 END) as accuracy_rate
                FROM neural_network_predictions
                WHERE prediction_date >= NOW() - INTERVAL %s
                AND actual_outcome IS NOT NULL
                GROUP BY is_hft
            """

            hft_results = self.db_service.execute_query(hft_query, (f"{days} days",), fetch=True)
            metrics['by_mode'] = []
            for row in hft_results:
                mode = 'HFT' if row[0] else 'Regular'
                metrics['by_mode'].append({
                    'mode': mode,
                    'total_predictions': row[1],
                    'correct_predictions': row[2],
                    'accuracy_rate': float(row[3]) if row[3] else 0.0
                })
            
            # Top performing stocks
            stock_query = """
                SELECT c.ticker,
                       COUNT(*) as total,
                       SUM(CASE WHEN p.prediction_accuracy THEN 1 ELSE 0 END) as correct,
                       AVG(CASE WHEN p.prediction_accuracy THEN 1.0 ELSE 0.0 END) as accuracy_rate
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.prediction_date >= NOW() - INTERVAL %s
                AND p.actual_outcome IS NOT NULL
                GROUP BY c.ticker
                HAVING COUNT(*) >= 3
                ORDER BY accuracy_rate DESC, total DESC
                LIMIT 10
            """

            stock_results = self.db_service.execute_query(stock_query, (f"{days} days",), fetch=True)
            metrics['top_stocks'] = []
            for row in stock_results:
                metrics['top_stocks'].append({
                    'ticker': row[0],
                    'total_predictions': row[1],
                    'correct_predictions': row[2],
                    'accuracy_rate': float(row[3]) if row[3] else 0.0
                })
            
            return metrics
            
        except Exception as e:
            print(f"❌ Error getting accuracy metrics: {e}")
            return {}
    
    def print_dashboard(self):
        """Print interactive dashboard"""
        print("\n" + "="*80)
        print("📊 STOCKTREK DATABASE MONITOR DASHBOARD")
        print("="*80)
        
        # Database statistics
        stats = self.get_database_stats()
        if stats:
            print(f"\n📈 DATABASE STATISTICS")
            print("-" * 40)
            print(f"Total Companies: {stats.get('total_companies', 0):,}")
            print(f"Total Predictions: {stats.get('total_predictions', 0):,}")
            print(f"Recent Predictions (24h): {stats.get('recent_predictions_24h', 0):,}")
            print(f"HFT Predictions (24h): {stats.get('hft_predictions_24h', 0):,}")
            print(f"Evaluated Predictions: {stats.get('evaluated_predictions', 0):,}")
            print(f"Overall Accuracy: {stats.get('accuracy_rate', 0):.1%}")
            
            if stats.get('latest_prediction'):
                latest = stats['latest_prediction']
                print(f"\n🔮 LATEST PREDICTION")
                print("-" * 40)
                print(f"Date: {latest['date']}")
                print(f"Ticker: {latest['ticker']}")
                print(f"Prediction: {latest['prediction']}")
                print(f"Confidence: {latest['confidence']:.1f}%")
        
        # Recent predictions
        print(f"\n📋 RECENT PREDICTIONS (Last 24 hours)")
        print("-" * 80)
        recent = self.get_recent_predictions(24, 10)
        if recent:
            print(f"{'Date':<19} {'Ticker':<6} {'Pred':<4} {'Conf':<5} {'Change':<8} {'HFT':<3} {'Acc':<3}")
            print("-" * 80)
            for pred in recent:
                date_str = pred['date'][-8:]  # Just time
                ticker = pred['ticker'][:5]
                prediction = pred['prediction'][:4]
                confidence = f"{pred['confidence']:.0f}%" if pred['confidence'] else "N/A"
                change = f"{pred['price_change_percent']:.1f}%" if pred['price_change_percent'] else "N/A"
                hft = "⚡" if pred['is_hft'] else "📊"
                accuracy = "✅" if pred['accuracy'] is True else "❌" if pred['accuracy'] is False else "⏳"
                
                print(f"{date_str:<19} {ticker:<6} {prediction:<4} {confidence:<5} {change:<8} {hft:<3} {accuracy:<3}")
        else:
            print("No recent predictions found")
        
        print("\n" + "="*80)
    
    def print_accuracy_report(self, days: int = 7):
        """Print detailed accuracy report"""
        print(f"\n📊 ACCURACY REPORT (Last {days} days)")
        print("="*60)
        
        metrics = self.get_accuracy_metrics(days)
        
        if metrics.get('by_timeframe'):
            print(f"\n⏰ ACCURACY BY TIMEFRAME")
            print("-" * 40)
            print(f"{'Timeframe':<12} {'Total':<8} {'Correct':<8} {'Accuracy':<10}")
            print("-" * 40)
            for tf in metrics['by_timeframe']:
                timeframe = f"{tf['timeframe_days']} days"
                total = tf['total_predictions']
                correct = tf['correct_predictions']
                accuracy = f"{tf['accuracy_rate']:.1%}"
                print(f"{timeframe:<12} {total:<8} {correct:<8} {accuracy:<10}")
        
        if metrics.get('by_mode'):
            print(f"\n⚡ ACCURACY BY MODE")
            print("-" * 40)
            print(f"{'Mode':<12} {'Total':<8} {'Correct':<8} {'Accuracy':<10}")
            print("-" * 40)
            for mode in metrics['by_mode']:
                mode_name = mode['mode']
                total = mode['total_predictions']
                correct = mode['correct_predictions']
                accuracy = f"{mode['accuracy_rate']:.1%}"
                print(f"{mode_name:<12} {total:<8} {correct:<8} {accuracy:<10}")
        
        if metrics.get('top_stocks'):
            print(f"\n🏆 TOP PERFORMING STOCKS")
            print("-" * 40)
            print(f"{'Ticker':<8} {'Total':<8} {'Correct':<8} {'Accuracy':<10}")
            print("-" * 40)
            for stock in metrics['top_stocks'][:10]:
                ticker = stock['ticker']
                total = stock['total_predictions']
                correct = stock['correct_predictions']
                accuracy = f"{stock['accuracy_rate']:.1%}"
                print(f"{ticker:<8} {total:<8} {correct:<8} {accuracy:<10}")
    
    def live_monitor(self, refresh_seconds: int = 30):
        """Live monitoring mode"""
        print("🔴 LIVE MONITORING MODE (Press Ctrl+C to exit)")
        print(f"Refreshing every {refresh_seconds} seconds...\n")
        
        try:
            while True:
                # Clear screen (works on most terminals)
                os.system('clear' if os.name == 'posix' else 'cls')
                
                print(f"🔴 LIVE MONITOR - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                self.print_dashboard()
                
                time.sleep(refresh_seconds)
                
        except KeyboardInterrupt:
            print("\n\n👋 Live monitoring stopped")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='StockTrek Database Monitor')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--predictions', action='store_true', help='Show recent predictions')
    parser.add_argument('--accuracy', action='store_true', help='Show accuracy metrics')
    parser.add_argument('--hft', action='store_true', help='Show HFT performance')
    parser.add_argument('--live', action='store_true', help='Live monitoring mode')
    parser.add_argument('--hours', type=int, default=24, help='Hours to look back (default: 24)')
    parser.add_argument('--days', type=int, default=7, help='Days to look back for accuracy (default: 7)')
    
    args = parser.parse_args()
    
    monitor = DatabaseMonitor()
    
    try:
        if args.live:
            monitor.live_monitor()
        elif args.stats:
            stats = monitor.get_database_stats()
            print(json.dumps(stats, indent=2, default=str))
        elif args.predictions:
            predictions = monitor.get_recent_predictions(args.hours)
            print(json.dumps(predictions, indent=2, default=str))
        elif args.accuracy:
            monitor.print_accuracy_report(args.days)
        elif args.hft:
            # Show HFT specific metrics
            predictions = monitor.get_recent_predictions(args.hours)
            hft_predictions = [p for p in predictions if p.get('is_hft')]
            print(f"HFT Predictions in last {args.hours} hours: {len(hft_predictions)}")
            for pred in hft_predictions[:10]:
                print(f"{pred['date']} - {pred['ticker']}: {pred['prediction']} ({pred['confidence']:.1f}%)")
        else:
            # Default: show dashboard
            monitor.print_dashboard()
            
    except Exception as e:
        print(f"❌ Monitor error: {e}")


if __name__ == "__main__":
    main()
