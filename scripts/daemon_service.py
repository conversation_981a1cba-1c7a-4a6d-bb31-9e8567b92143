#!/usr/bin/env python3
"""
StockTrek Daemon Service - 24/7 Persistent Stock Prediction System

This daemon runs continuously in the background, managing:
1. Scheduled predictions and evaluations
2. Autonomous market scanning
3. Automatic learning from prediction outcomes
4. System health monitoring
5. Error recovery and resilience

Usage:
    python3 daemon_service.py start    # Start daemon
    python3 daemon_service.py stop     # Stop daemon
    python3 daemon_service.py status   # Check status
    python3 daemon_service.py restart  # Restart daemon
"""

import os
import sys
import time
import signal
import logging
import threading
import schedule
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional
import psutil

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from ETL.prediction_service import PredictionService
from ETL.autonomous_scanner import AutonomousScanner
from ETL.backtesting_service import BacktestingService
from ETL.autonomous_training_manager import AutonomousTrainingManager
from Databases.database_service import DatabaseService
from Neural_Network.neural_network_service import NeuralNetworkService

class StockTrekDaemon:
    """24/7 Persistent StockTrek Daemon Service"""
    
    def __init__(self):
        self.pid_file = Path("stocktrek_daemon.pid")
        self.log_file = Path("logs/daemon.log")
        self.config_file = Path("config/daemon_config.json")
        self.running = False
        self.threads = []
        
        # Ensure directories exist
        self.log_file.parent.mkdir(exist_ok=True)
        self.config_file.parent.mkdir(exist_ok=True)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self.config = self.load_config()
        
        # Initialize services
        self.prediction_service = None
        self.autonomous_scanner = None
        self.backtesting_service = None
        self.autonomous_training_manager = None
        self.db_service = None
        self.neural_network = None
        
    def load_config(self) -> Dict:
        """Load daemon configuration"""
        default_config = {
            "autonomous_scan_interval": 300,  # 5 minutes
            "prediction_check_interval": 3600,  # 1 hour
            "health_check_interval": 60,  # 1 minute
            "max_retries": 3,
            "retry_delay": 30,
            "log_level": "INFO",
            "enable_autonomous_scanning": True,
            "enable_prediction_evaluation": True,
            "enable_auto_learning": True,
            "market_hours": {
                "start": "09:30",
                "end": "16:00",
                "timezone": "US/Eastern"
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    default_config.update(config)
            except Exception as e:
                self.logger.warning(f"Error loading config: {e}, using defaults")
        else:
            # Save default config
            with open(self.config_file, 'w') as f:
                json.dump(default_config, f, indent=2)
                
        return default_config
    
    def initialize_services(self):
        """Initialize all StockTrek services"""
        try:
            self.logger.info("Initializing StockTrek services...")
            
            # Initialize database service
            self.db_service = DatabaseService()
            
            # Initialize neural network
            self.neural_network = NeuralNetworkService()
            
            # Initialize prediction service
            self.prediction_service = PredictionService()
            
            # Initialize autonomous scanner
            self.autonomous_scanner = AutonomousScanner()
            
            # Initialize backtesting service
            self.backtesting_service = BacktestingService()

            # Initialize autonomous training manager
            self.autonomous_training_manager = AutonomousTrainingManager()

            self.logger.info("✅ All services initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize services: {e}")
            return False
    
    def start_daemon(self):
        """Start the daemon process with automatic HFT training"""
        if self.is_running():
            print("Daemon is already running")
            return

        # Write PID file
        with open(self.pid_file, 'w') as f:
            f.write(str(os.getpid()))

        self.logger.info("🚀 Starting StockTrek Daemon with HFT Auto-Training")

        # Initialize services
        if not self.initialize_services():
            self.logger.error("Failed to initialize services, exiting")
            return

        # Setup signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)

        # Setup scheduled tasks
        self.setup_scheduler()

        # Start background threads
        self.start_background_threads()

        # Start HFT training mode automatically
        self.start_hft_training_thread()

        # Setup automatic data exports to desktop
        self.setup_desktop_exports()

        self.running = True
        self.logger.info("✅ StockTrek Daemon started successfully")
        self.logger.info("⚡ HFT Auto-Training: ACTIVE")
        self.logger.info("📦 Desktop Exports: Every 4 hours")

        # Main daemon loop
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Received interrupt signal")
        finally:
            self.shutdown()
    
    def setup_scheduler(self):
        """Setup scheduled tasks"""
        if self.config.get("enable_autonomous_scanning", True):
            schedule.every(self.config["autonomous_scan_interval"]).seconds.do(
                self.autonomous_scan_task
            )
            
        if self.config.get("enable_prediction_evaluation", True):
            schedule.every(self.config["prediction_check_interval"]).seconds.do(
                self.prediction_evaluation_task
            )
            
        schedule.every(self.config["health_check_interval"]).seconds.do(
            self.health_check_task
        )
        
        # Daily tasks
        schedule.every().day.at("00:00").do(self.daily_maintenance_task)
        schedule.every().day.at("06:00").do(self.daily_learning_task)

        # Autonomous training tasks
        schedule.every().day.at("17:00").do(self.update_stock_list_task)
        schedule.every().day.at("06:00").do(self.reset_training_task)

        self.logger.info("📅 Scheduled tasks configured")
    
    def start_background_threads(self):
        """Start background monitoring threads"""
        # System monitoring thread
        monitor_thread = threading.Thread(target=self.system_monitor_loop, daemon=True)
        monitor_thread.start()
        self.threads.append(monitor_thread)

        self.logger.info("🔄 Background threads started")

    def start_hft_training_thread(self):
        """Start HFT training in background thread"""
        try:
            # Start new autonomous training system
            if self.autonomous_training_manager:
                self.logger.info("🤖 Starting autonomous training system...")
                if self.autonomous_training_manager.start_autonomous_training():
                    self.logger.info("✅ Autonomous training system started")
                else:
                    self.logger.error("❌ Failed to start autonomous training system")

            # Legacy HFT training support
            hft_thread = threading.Thread(target=self.hft_training_loop, daemon=True)
            hft_thread.start()
            self.threads.append(hft_thread)
            self.logger.info("⚡ HFT Training thread started")
        except Exception as e:
            self.logger.error(f"Failed to start HFT training: {e}")

    def hft_training_loop(self):
        """Continuous HFT training loop"""
        try:
            import subprocess
            import time

            self.logger.info("� Starting HFT Auto-Training Mode")

            while self.running:
                try:
                    # Run main.py in HFT mode for 30 minutes, then restart
                    self.logger.info("⚡ Starting HFT training cycle")

                    # Start HFT process
                    process = subprocess.Popen(
                        [sys.executable, "main.py", "--hft"],
                        cwd=str(Path(__file__).parent),
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )

                    # Let it run for 30 minutes
                    time.sleep(1800)  # 30 minutes

                    # Terminate and restart
                    process.terminate()
                    process.wait(timeout=10)

                    self.logger.info("�🔄 HFT training cycle completed, restarting...")
                    time.sleep(60)  # 1 minute break between cycles

                except Exception as e:
                    self.logger.error(f"HFT training cycle error: {e}")
                    time.sleep(300)  # 5 minute delay on error

        except Exception as e:
            self.logger.error(f"HFT training loop error: {e}")

    def setup_desktop_exports(self):
        """Setup automatic exports to desktop every 4 hours"""
        try:
            # Schedule exports every 4 hours
            schedule.every(4).hours.do(self.export_to_desktop)
            self.logger.info("📦 Desktop exports scheduled every 4 hours")
        except Exception as e:
            self.logger.error(f"Failed to setup desktop exports: {e}")

    def export_to_desktop(self):
        """Export data to StockTrek Data folder on desktop"""
        try:
            import subprocess
            import shutil
            from pathlib import Path

            self.logger.info("📦 Starting desktop export...")

            # Create desktop folder
            desktop_path = Path.home() / "Desktop" / "StockTrek Data"
            desktop_path.mkdir(exist_ok=True)

            # Generate export
            result = subprocess.run(
                [sys.executable, "main.py", "--export", "complete", "--export-days", "7"],
                cwd=str(Path(__file__).parent),
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                # Find the latest export file
                exports_dir = Path(__file__).parent / "exports"
                if exports_dir.exists():
                    zip_files = list(exports_dir.glob("stocktrek_complete_export_*.zip"))
                    if zip_files:
                        latest_export = max(zip_files, key=lambda x: x.stat().st_mtime)

                        # Copy to desktop with timestamp
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M")
                        desktop_file = desktop_path / f"StockTrek_Export_{timestamp}.zip"
                        shutil.copy2(latest_export, desktop_file)

                        self.logger.info(f"✅ Export copied to desktop: {desktop_file}")
                    else:
                        self.logger.warning("No export files found")
                else:
                    self.logger.warning("Exports directory not found")
            else:
                self.logger.error(f"Export failed: {result.stderr}")

        except Exception as e:
            self.logger.error(f"Desktop export error: {e}")
    
    def autonomous_scan_task(self):
        """Autonomous market scanning task"""
        try:
            self.logger.info("🔍 Running autonomous market scan")
            if self.autonomous_scanner:
                # Get default scan symbols
                symbols = self.autonomous_scanner.get_scan_list()
                if symbols:
                    results = self.autonomous_scanner.scan_market_multi_timeframe(symbols)
                    self.logger.info(f"Scanned {len(symbols)} stocks")
                else:
                    self.logger.warning("No symbols available for scanning")
        except Exception as e:
            self.logger.error(f"Error in autonomous scan: {e}")
    
    def prediction_evaluation_task(self):
        """Check and evaluate matured predictions"""
        try:
            self.logger.info("📊 Evaluating matured predictions")
            if self.backtesting_service:
                results = self.backtesting_service.evaluate_matured_predictions()
                if results:
                    self.logger.info(f"Evaluated {len(results)} predictions")
        except Exception as e:
            self.logger.error(f"Error evaluating predictions: {e}")
    
    def health_check_task(self):
        """System health check"""
        try:
            # Check database connection
            if self.db_service and not self.db_service.test_connection():
                self.logger.warning("Database connection lost, attempting reconnect")
                self.db_service.reconnect()
                
            # Check memory usage
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 80:
                self.logger.warning(f"High memory usage: {memory_percent}%")
                
        except Exception as e:
            self.logger.error(f"Health check error: {e}")
    
    def daily_maintenance_task(self):
        """Daily maintenance tasks"""
        try:
            self.logger.info("🧹 Running daily maintenance")
            # Clean old logs
            self.cleanup_old_logs()
            # Database maintenance
            if self.db_service:
                self.db_service.maintenance()
        except Exception as e:
            self.logger.error(f"Daily maintenance error: {e}")
    
    def daily_learning_task(self):
        """Daily learning and model updates"""
        try:
            self.logger.info("🧠 Running daily learning task")
            if self.neural_network and self.config.get("enable_auto_learning", True):
                # Trigger learning from recent predictions
                self.neural_network.learn_from_recent_predictions()
        except Exception as e:
            self.logger.error(f"Daily learning error: {e}")

    def update_stock_list_task(self):
        """Scheduled stock list update task"""
        try:
            self.logger.info("📊 Running scheduled stock list update...")
            if self.autonomous_training_manager:
                self.autonomous_training_manager.update_stock_list()
                self.logger.info("✅ Stock list update completed")
            else:
                self.logger.warning("⚠️ Autonomous training manager not available")
        except Exception as e:
            self.logger.error(f"Error in stock list update task: {e}")

    def reset_training_task(self):
        """Scheduled daily training reset task"""
        try:
            self.logger.info("🔄 Running scheduled daily training reset...")
            if self.autonomous_training_manager:
                self.autonomous_training_manager.reset_daily_training()
                self.logger.info("✅ Daily training reset completed")
            else:
                self.logger.warning("⚠️ Autonomous training manager not available")
        except Exception as e:
            self.logger.error(f"Error in daily training reset task: {e}")

    def system_monitor_loop(self):
        """Background system monitoring"""
        while self.running:
            try:
                # Monitor system resources
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                if cpu_percent > 90:
                    self.logger.warning(f"High CPU usage: {cpu_percent}%")
                if memory_percent > 90:
                    self.logger.warning(f"High memory usage: {memory_percent}%")
                    
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"System monitor error: {e}")
                time.sleep(60)
    
    def cleanup_old_logs(self):
        """Clean up old log files"""
        try:
            log_dir = self.log_file.parent
            cutoff_date = datetime.now() - timedelta(days=30)
            
            for log_file in log_dir.glob("*.log.*"):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    self.logger.info(f"Deleted old log: {log_file}")
        except Exception as e:
            self.logger.error(f"Log cleanup error: {e}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        self.logger.info("🛑 Shutting down StockTrek Daemon")
        self.running = False
        
        # Wait for threads to finish
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=5)
        
        # Remove PID file
        if self.pid_file.exists():
            self.pid_file.unlink()
            
        self.logger.info("✅ Daemon shutdown complete")
    
    def is_running(self) -> bool:
        """Check if daemon is already running"""
        if not self.pid_file.exists():
            return False
            
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            return psutil.pid_exists(pid)
        except (ValueError, FileNotFoundError):
            return False
    
    def stop_daemon(self):
        """Stop the daemon"""
        if not self.is_running():
            print("Daemon is not running")
            return
            
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            os.kill(pid, signal.SIGTERM)
            print("Daemon stopped")
        except (FileNotFoundError, ProcessLookupError, ValueError):
            print("Error stopping daemon")
    
    def status(self):
        """Show daemon status"""
        if self.is_running():
            print("✅ StockTrek Daemon is running")
            try:
                with open(self.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                process = psutil.Process(pid)
                print(f"   PID: {pid}")
                print(f"   CPU: {process.cpu_percent()}%")
                print(f"   Memory: {process.memory_percent():.1f}%")
                print(f"   Started: {datetime.fromtimestamp(process.create_time())}")
            except Exception as e:
                print(f"   Error getting process info: {e}")
        else:
            print("❌ StockTrek Daemon is not running")

def main():
    """Main entry point"""
    daemon = StockTrekDaemon()
    
    if len(sys.argv) != 2:
        print("Usage: python3 daemon_service.py {start|stop|restart|status}")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == "start":
        daemon.start_daemon()
    elif command == "stop":
        daemon.stop_daemon()
    elif command == "restart":
        daemon.stop_daemon()
        time.sleep(2)
        daemon.start_daemon()
    elif command == "status":
        daemon.status()
    else:
        print("Invalid command. Use: start|stop|restart|status")
        sys.exit(1)

if __name__ == "__main__":
    main()
