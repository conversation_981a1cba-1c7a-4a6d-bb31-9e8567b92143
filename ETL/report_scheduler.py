"""
StockTrek Report Scheduler
Automated report generation for continuous investor updates
"""

import os
import json
import logging
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any

from ETL.data_export_service import DataExportService

logger = logging.getLogger(__name__)

class ReportScheduler:
    """Automated report generation scheduler"""
    
    def __init__(self):
        self.export_service = DataExportService()
        self.config_file = Path("config/report_schedule.json")
        self.config = self.load_config()
        self.running = False
        
        logger.info("📅 Report Scheduler initialized")
    
    def load_config(self) -> Dict[str, Any]:
        """Load report scheduling configuration"""
        default_config = {
            "daily_reports": {
                "enabled": True,
                "time": "06:00",
                "days_back": 7,
                "format": "quick"
            },
            "weekly_reports": {
                "enabled": True,
                "day": "monday",
                "time": "08:00",
                "days_back": 30,
                "format": "complete"
            },
            "monthly_reports": {
                "enabled": True,
                "day": 1,
                "time": "09:00",
                "days_back": 90,
                "format": "investor"
            },
            "cleanup": {
                "enabled": True,
                "keep_days": 30,
                "time": "02:00"
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            except Exception as e:
                logger.warning(f"Error loading config, using defaults: {e}")
        
        # Create config file with defaults
        self.config_file.parent.mkdir(exist_ok=True)
        with open(self.config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        return default_config
    
    def setup_schedules(self):
        """Setup all scheduled reports"""
        logger.info("⏰ Setting up report schedules...")
        
        # Daily reports
        if self.config["daily_reports"]["enabled"]:
            schedule.every().day.at(self.config["daily_reports"]["time"]).do(
                self.generate_daily_report
            )
            logger.info(f"   📊 Daily reports: {self.config['daily_reports']['time']}")
        
        # Weekly reports
        if self.config["weekly_reports"]["enabled"]:
            day = self.config["weekly_reports"]["day"].lower()
            time_str = self.config["weekly_reports"]["time"]
            
            if day == "monday":
                schedule.every().monday.at(time_str).do(self.generate_weekly_report)
            elif day == "tuesday":
                schedule.every().tuesday.at(time_str).do(self.generate_weekly_report)
            elif day == "wednesday":
                schedule.every().wednesday.at(time_str).do(self.generate_weekly_report)
            elif day == "thursday":
                schedule.every().thursday.at(time_str).do(self.generate_weekly_report)
            elif day == "friday":
                schedule.every().friday.at(time_str).do(self.generate_weekly_report)
            elif day == "saturday":
                schedule.every().saturday.at(time_str).do(self.generate_weekly_report)
            elif day == "sunday":
                schedule.every().sunday.at(time_str).do(self.generate_weekly_report)
            
            logger.info(f"   📈 Weekly reports: {day} at {time_str}")
        
        # Monthly reports (simplified - run on 1st of month)
        if self.config["monthly_reports"]["enabled"]:
            schedule.every().day.at(self.config["monthly_reports"]["time"]).do(
                self.check_monthly_report
            )
            logger.info(f"   📋 Monthly reports: 1st of month at {self.config['monthly_reports']['time']}")
        
        # Cleanup
        if self.config["cleanup"]["enabled"]:
            schedule.every().day.at(self.config["cleanup"]["time"]).do(
                self.cleanup_old_reports
            )
            logger.info(f"   🧹 Cleanup: daily at {self.config['cleanup']['time']}")
        
        logger.info("✅ All schedules configured")
    
    def generate_daily_report(self):
        """Generate daily performance report"""
        try:
            logger.info("📊 Generating daily report...")
            
            timestamp = datetime.now().strftime("%Y%m%d")
            report_dir = Path("exports/scheduled") / "daily" / timestamp
            report_dir.mkdir(parents=True, exist_ok=True)
            
            days_back = self.config["daily_reports"]["days_back"]
            
            # Generate quick report
            self.export_service.export_predictions_data(report_dir, days_back)
            self.export_service.export_performance_metrics(report_dir, days_back)
            self.export_service.generate_summary_report(report_dir, days_back)
            
            # Create notification file
            self.create_notification(report_dir, "daily", days_back)
            
            logger.info(f"✅ Daily report generated: {report_dir}")
            
        except Exception as e:
            logger.error(f"❌ Daily report failed: {e}")
    
    def generate_weekly_report(self):
        """Generate weekly comprehensive report"""
        try:
            logger.info("📈 Generating weekly report...")
            
            timestamp = datetime.now().strftime("%Y%m%d")
            report_dir = Path("exports/scheduled") / "weekly" / timestamp
            report_dir.mkdir(parents=True, exist_ok=True)
            
            days_back = self.config["weekly_reports"]["days_back"]
            
            # Generate complete report
            self.export_service.export_predictions_data(report_dir, days_back)
            self.export_service.export_performance_metrics(report_dir, days_back)
            self.export_service.export_learning_feedback(report_dir, days_back)
            self.export_service.export_company_data(report_dir)
            self.export_service.export_daemon_activity(report_dir, days_back)
            self.export_service.generate_summary_report(report_dir, days_back)
            
            # Create ZIP archive
            zip_path = self.export_service.create_zip_archive(report_dir, f"weekly_report_{timestamp}")
            
            # Create notification file
            self.create_notification(report_dir, "weekly", days_back, zip_path)
            
            logger.info(f"✅ Weekly report generated: {zip_path}")
            
        except Exception as e:
            logger.error(f"❌ Weekly report failed: {e}")
    
    def check_monthly_report(self):
        """Check if monthly report should be generated"""
        if datetime.now().day == 1:  # First day of month
            self.generate_monthly_report()
    
    def generate_monthly_report(self):
        """Generate monthly investor report"""
        try:
            logger.info("📋 Generating monthly investor report...")
            
            timestamp = datetime.now().strftime("%Y%m")
            report_dir = Path("exports/scheduled") / "monthly" / timestamp
            report_dir.mkdir(parents=True, exist_ok=True)
            
            days_back = self.config["monthly_reports"]["days_back"]
            
            # Generate full investor package
            zip_path = self.export_service.export_all_data(days_back)
            
            # Move to monthly directory
            monthly_zip = report_dir / f"investor_report_{timestamp}.zip"
            os.rename(zip_path, monthly_zip)
            
            # Create notification file
            self.create_notification(report_dir, "monthly", days_back, monthly_zip)
            
            logger.info(f"✅ Monthly investor report generated: {monthly_zip}")
            
        except Exception as e:
            logger.error(f"❌ Monthly report failed: {e}")
    
    def cleanup_old_reports(self):
        """Clean up old report files"""
        try:
            logger.info("🧹 Cleaning up old reports...")
            
            cutoff_date = datetime.now() - timedelta(days=self.config["cleanup"]["keep_days"])
            exports_dir = Path("exports")
            
            if not exports_dir.exists():
                return
            
            cleaned_count = 0
            for file_path in exports_dir.rglob("*"):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        try:
                            file_path.unlink()
                            cleaned_count += 1
                        except Exception as e:
                            logger.warning(f"Could not delete {file_path}: {e}")
            
            logger.info(f"✅ Cleanup complete: {cleaned_count} files removed")
            
        except Exception as e:
            logger.error(f"❌ Cleanup failed: {e}")
    
    def create_notification(self, report_dir: Path, report_type: str, days_back: int, zip_path: Path = None):
        """Create notification file for report"""
        notification = {
            "report_type": report_type,
            "generated_at": datetime.now().isoformat(),
            "days_analyzed": days_back,
            "report_directory": str(report_dir),
            "zip_file": str(zip_path) if zip_path else None,
            "status": "completed"
        }
        
        with open(report_dir / "notification.json", 'w') as f:
            json.dump(notification, f, indent=2)
    
    def run_scheduler(self):
        """Run the report scheduler"""
        self.setup_schedules()
        self.running = True
        
        logger.info("🚀 Report scheduler started")
        logger.info("   📅 Automated reports will be generated according to schedule")
        logger.info("   📁 Reports saved to: exports/scheduled/")
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("⏹️ Report scheduler stopped by user")
        except Exception as e:
            logger.error(f"❌ Scheduler error: {e}")
        finally:
            self.running = False
    
    def stop_scheduler(self):
        """Stop the report scheduler"""
        self.running = False
        logger.info("⏹️ Report scheduler stopping...")

def main():
    """Main entry point for standalone scheduler"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/report_scheduler.log', mode='a')
        ]
    )
    
    scheduler = ReportScheduler()
    scheduler.run_scheduler()

if __name__ == "__main__":
    main()
