#!/usr/bin/env python3
"""
Autonomous Training Manager for StockTrek
Manages efficient stock list and autonomous training system
"""

import os
import sys
import time
import logging
import threading
import schedule
from datetime import datetime, timedelta, time as dt_time
from typing import Dict, List, Optional, Tuple
import pytz
import yfinance as yf
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from Databases.database_service import DatabaseService
from Neural_Network.neural_network_service import NeuralNetworkService
from ETL.backtesting_service import BacktestingService
from Preprocessing.sentiment import SentimentAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AutonomousTrainingManager:
    """
    Manages autonomous training system with efficient stock list management
    Supports HFT mode (5-minute predictions) and Daily mode (once after market close)
    """
    
    def __init__(self):
        """Initialize the autonomous training manager"""
        self.db_service = DatabaseService()
        self.neural_network = NeuralNetworkService()
        self.backtesting_service = BacktestingService()
        self.sentiment_analyzer = SentimentAnalyzer()
        
        # Market timezone
        self.market_tz = pytz.timezone('US/Eastern')
        
        # Training state
        self.is_running = False
        self.hft_thread = None
        self.daily_thread = None
        
        # Configuration cache
        self.config_cache = {}
        self._load_configuration()
        
        logger.info("🤖 Autonomous Training Manager initialized")
        logger.info(f"⚡ HFT Mode: {self.get_config('enable_hft_mode', 'true') == 'true'}")
        logger.info(f"📅 Daily Mode: {self.get_config('enable_daily_mode', 'true') == 'true'}")
    
    def _load_configuration(self):
        """Load configuration from database"""
        try:
            query = "SELECT config_key, config_value, config_type FROM autonomous_training_config"
            configs = self.db_service.execute_query(query, fetch=True)
            
            for config in configs:
                key = config['config_key']
                value = config['config_value']
                config_type = config['config_type']
                
                # Convert based on type
                if config_type == 'integer':
                    value = int(value)
                elif config_type == 'boolean':
                    value = value.lower() == 'true'
                elif config_type == 'json':
                    import json
                    value = json.loads(value)
                
                self.config_cache[key] = value
                
            logger.info(f"📋 Loaded {len(self.config_cache)} configuration settings")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            # Set defaults
            self.config_cache = {
                'hft_prediction_interval_minutes': 5,
                'hft_backtest_interval_minutes': 5,
                'daily_mode_start_time': '16:30',
                'max_hft_predictions_per_stock': 50,
                'stock_list_update_time': '17:00',
                'training_batch_size': 50,
                'enable_hft_mode': True,
                'enable_daily_mode': True,
                'market_open_time': '09:30',
                'market_close_time': '16:00'
            }
    
    def get_config(self, key: str, default=None):
        """Get configuration value"""
        return self.config_cache.get(key, default)
    
    def update_stock_list(self) -> bool:
        """Update stock list from market data (IPOs/delistings)"""
        try:
            logger.info("📊 Updating stock list for IPOs/delistings...")
            
            # Get current stocks from database
            current_stocks = self.get_current_stock_list()
            current_tickers = {stock['ticker'] for stock in current_stocks}
            
            # Get comprehensive ticker list from market
            new_tickers = self._fetch_comprehensive_tickers()
            
            # Find new stocks (IPOs)
            new_stocks = new_tickers - current_tickers
            
            # Find delisted stocks
            delisted_stocks = current_tickers - new_tickers
            
            # Add new stocks
            added_count = 0
            for ticker in new_stocks:
                if self._add_stock_to_database(ticker):
                    added_count += 1
            
            # Mark delisted stocks (don't delete, just mark inactive)
            delisted_count = 0
            for ticker in delisted_stocks:
                if self._mark_stock_delisted(ticker):
                    delisted_count += 1
            
            # Update training queue
            self._update_training_queue()
            
            logger.info(f"✅ Stock list updated: +{added_count} new, -{delisted_count} delisted")
            return True
            
        except Exception as e:
            logger.error(f"Error updating stock list: {e}")
            return False
    
    def _fetch_comprehensive_tickers(self) -> set:
        """Fetch comprehensive ticker list from multiple sources"""
        tickers = set()
        
        try:
            # Get S&P 500
            sp500_url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            import pandas as pd
            sp500_table = pd.read_html(sp500_url)[0]
            sp500_tickers = sp500_table['Symbol'].tolist()
            tickers.update(sp500_tickers)
            logger.info(f"📈 Added {len(sp500_tickers)} S&P 500 tickers")
            
        except Exception as e:
            logger.warning(f"Failed to fetch S&P 500 tickers: {e}")
        
        try:
            # Get NASDAQ tickers
            nasdaq_url = "ftp://ftp.nasdaqtrader.com/SymbolDirectory/nasdaqlisted.txt"
            import pandas as pd
            nasdaq_df = pd.read_csv(nasdaq_url, sep='|')
            nasdaq_tickers = nasdaq_df['Symbol'].dropna().tolist()
            # Remove test symbols and ETFs
            nasdaq_tickers = [t for t in nasdaq_tickers if not t.endswith(('$', '.', 'TEST'))]
            tickers.update(nasdaq_tickers[:1000])  # Limit to prevent overload
            logger.info(f"📊 Added {len(nasdaq_tickers[:1000])} NASDAQ tickers")
            
        except Exception as e:
            logger.warning(f"Failed to fetch NASDAQ tickers: {e}")
        
        # Add fallback comprehensive list if we didn't get enough
        if len(tickers) < 100:
            fallback_tickers = self._get_fallback_tickers()
            tickers.update(fallback_tickers)
            logger.info(f"🔄 Added {len(fallback_tickers)} fallback tickers")
        
        return tickers
    
    def _get_fallback_tickers(self) -> List[str]:
        """Get fallback comprehensive ticker list"""
        return [
            # Major tech stocks
            'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'META', 'NVDA', 'TSLA',
            # Financial sector
            'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'BLK', 'AXP',
            # Healthcare
            'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR',
            # Consumer goods
            'PG', 'KO', 'PEP', 'WMT', 'HD', 'MCD', 'NKE', 'COST',
            # Energy
            'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC',
            # Popular penny stocks
            'SNDL', 'NAKD', 'GNUS', 'XSPA', 'SHIP', 'TOPS', 'GLBS', 'CTRM'
        ]
    
    def _add_stock_to_database(self, ticker: str) -> bool:
        """Add new stock to database"""
        try:
            # Check if already exists
            query = "SELECT id FROM companies WHERE ticker = %s"
            existing = self.db_service.execute_query(query, (ticker,), fetch=True)
            
            if existing:
                return False  # Already exists
            
            # Get company name from yfinance
            try:
                stock = yf.Ticker(ticker)
                info = stock.info
                company_name = info.get('longName', ticker)
            except:
                company_name = ticker
            
            # Insert into companies table
            insert_query = "INSERT INTO companies (name, ticker) VALUES (%s, %s) RETURNING id"
            result = self.db_service.execute_query(insert_query, (company_name, ticker), fetch=True)
            
            if result:
                logger.info(f"➕ Added new stock: {ticker} ({company_name})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error adding stock {ticker}: {e}")
            return False
    
    def _mark_stock_delisted(self, ticker: str) -> bool:
        """Mark stock as delisted (don't delete historical data)"""
        try:
            # For now, we'll just log it. In production, you might want to add a 'delisted' flag
            logger.info(f"📉 Stock delisted: {ticker}")
            return True
            
        except Exception as e:
            logger.error(f"Error marking stock delisted {ticker}: {e}")
            return False
    
    def get_current_stock_list(self) -> List[Dict]:
        """Get current stock list from database"""
        try:
            query = """
                SELECT c.id, c.ticker, c.name 
                FROM companies c 
                ORDER BY c.ticker
            """
            stocks = self.db_service.execute_query(query, fetch=True)
            return stocks or []
            
        except Exception as e:
            logger.error(f"Error getting stock list: {e}")
            return []
    
    def _update_training_queue(self):
        """Update training queue with current stocks"""
        try:
            # Get all companies
            stocks = self.get_current_stock_list()
            
            # Clear today's training queue
            today = datetime.now().date()
            self.db_service.execute_query(
                "DELETE FROM training_queue WHERE DATE(created_at) = %s", 
                (today,)
            )
            
            # Add all stocks to training queue
            for stock in stocks:
                insert_query = """
                    INSERT INTO training_queue (company_id, ticker, priority, training_mode)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (company_id) DO UPDATE SET
                        training_mode = 'pending',
                        sentiment_analyzed_today = FALSE,
                        hft_predictions_today = 0,
                        daily_prediction_completed = FALSE,
                        updated_at = CURRENT_TIMESTAMP
                """
                self.db_service.execute_query(
                    insert_query, 
                    (stock['id'], stock['ticker'], 1, 'pending')
                )
            
            # Initialize training status for today
            status_query = """
                INSERT INTO training_status (training_date, total_stocks)
                VALUES (%s, %s)
                ON CONFLICT (training_date) DO UPDATE SET
                    total_stocks = %s,
                    updated_at = CURRENT_TIMESTAMP
            """
            self.db_service.execute_query(
                status_query, 
                (today, len(stocks), len(stocks))
            )
            
            logger.info(f"📋 Training queue updated with {len(stocks)} stocks")

        except Exception as e:
            logger.error(f"Error updating training queue: {e}")

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            now = datetime.now(self.market_tz)

            # Check if it's a weekday
            if now.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return False

            # Get market hours from config
            open_time_str = self.get_config('market_open_time', '09:30')
            close_time_str = self.get_config('market_close_time', '16:00')

            open_time = dt_time(*map(int, open_time_str.split(':')))
            close_time = dt_time(*map(int, close_time_str.split(':')))

            current_time = now.time()

            return open_time <= current_time <= close_time

        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False

    def get_next_hft_batch(self, batch_size: int = None) -> List[Dict]:
        """Get next batch of stocks for HFT training"""
        try:
            if batch_size is None:
                batch_size = self.get_config('training_batch_size', 50)

            max_predictions = self.get_config('max_hft_predictions_per_stock', 50)

            query = """
                SELECT tq.id, tq.company_id, tq.ticker, c.name
                FROM training_queue tq
                JOIN companies c ON tq.company_id = c.id
                WHERE tq.training_mode IN ('pending', 'hft')
                AND tq.hft_predictions_today < %s
                AND tq.sentiment_analyzed_today = FALSE
                ORDER BY tq.priority ASC, tq.last_processed_at ASC NULLS FIRST
                LIMIT %s
            """

            stocks = self.db_service.execute_query(
                query, (max_predictions, batch_size), fetch=True
            )

            return stocks or []

        except Exception as e:
            logger.error(f"Error getting HFT batch: {e}")
            return []

    def get_daily_batch(self, batch_size: int = None) -> List[Dict]:
        """Get batch of stocks for daily training (not processed by HFT)"""
        try:
            if batch_size is None:
                batch_size = self.get_config('training_batch_size', 50)

            query = """
                SELECT tq.id, tq.company_id, tq.ticker, c.name
                FROM training_queue tq
                JOIN companies c ON tq.company_id = c.id
                WHERE tq.training_mode = 'pending'
                AND tq.daily_prediction_completed = FALSE
                ORDER BY tq.priority ASC, tq.last_processed_at ASC NULLS FIRST
                LIMIT %s
            """

            stocks = self.db_service.execute_query(query, (batch_size,), fetch=True)
            return stocks or []

        except Exception as e:
            logger.error(f"Error getting daily batch: {e}")
            return []

    def process_hft_prediction(self, stock: Dict) -> bool:
        """Process single HFT prediction (5-minute timeframe)"""
        try:
            ticker = stock['ticker']
            company_id = stock['company_id']

            logger.info(f"⚡ HFT Processing: {ticker}")

            # Check sentiment limit (one per day)
            if not self._check_sentiment_limit(company_id, ticker):
                return False

            # Make 5-minute prediction
            prediction = self.neural_network.predict_stock_with_etl(ticker, timeframe_days=0.0035)  # ~5 minutes in days

            if not prediction:
                logger.warning(f"❌ HFT prediction failed for {ticker}")
                return False

            # Store prediction with HFT flag
            prediction['is_hft'] = True
            prediction['timeframe_minutes'] = 5

            # Update training queue
            self._update_training_queue_status(stock['id'], 'hft', hft_increment=True)

            # Schedule backtest in 5 minutes
            self._schedule_hft_backtest(prediction, ticker, company_id)

            logger.info(f"✅ HFT prediction completed for {ticker}: {prediction.get('direction', 'N/A')}")
            return True

        except Exception as e:
            logger.error(f"Error in HFT prediction for {stock.get('ticker', 'unknown')}: {e}")
            return False

    def process_daily_prediction(self, stock: Dict) -> bool:
        """Process single daily prediction (after market close)"""
        try:
            ticker = stock['ticker']
            company_id = stock['company_id']

            logger.info(f"📅 Daily Processing: {ticker}")

            # Check sentiment limit (one per day)
            if not self._check_sentiment_limit(company_id, ticker):
                return False

            # Make daily prediction (1-day timeframe)
            prediction = self.neural_network.predict_stock_with_etl(ticker, timeframe_days=1)

            if not prediction:
                logger.warning(f"❌ Daily prediction failed for {ticker}")
                return False

            # Store prediction
            prediction['is_hft'] = False
            prediction['timeframe_days'] = 1

            # Update training queue
            self._update_training_queue_status(stock['id'], 'daily', daily_complete=True)

            logger.info(f"✅ Daily prediction completed for {ticker}: {prediction.get('direction', 'N/A')}")
            return True

        except Exception as e:
            logger.error(f"Error in daily prediction for {stock.get('ticker', 'unknown')}: {e}")
            return False

    def _check_sentiment_limit(self, company_id: int, ticker: str) -> bool:
        """Check if sentiment analysis already done today for this stock"""
        try:
            today = datetime.now().date()

            # Check if sentiment already analyzed today
            query = """
                SELECT id FROM sentiment_tracking
                WHERE company_id = %s AND sentiment_date = %s
            """
            existing = self.db_service.execute_query(query, (company_id, today), fetch=True)

            if existing:
                logger.debug(f"📊 Sentiment already analyzed today for {ticker}")
                return True  # Already done, but allow prediction

            # Perform sentiment analysis
            sentiment_score = self.sentiment_analyzer.analyze_stock_sentiment(ticker)

            # Store sentiment tracking
            insert_query = """
                INSERT INTO sentiment_tracking (
                    company_id, ticker, sentiment_date, sentiment_score,
                    sentiment_analysis_completed, sentiment_source
                ) VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (company_id, sentiment_date) DO UPDATE SET
                    sentiment_score = %s,
                    sentiment_analysis_completed = TRUE,
                    updated_at = CURRENT_TIMESTAMP
            """

            self.db_service.execute_query(
                insert_query,
                (company_id, ticker, today, sentiment_score, True, 'autonomous', sentiment_score)
            )

            # Update training queue sentiment flag
            self.db_service.execute_query(
                "UPDATE training_queue SET sentiment_analyzed_today = TRUE WHERE company_id = %s",
                (company_id,)
            )

            logger.debug(f"📊 Sentiment analysis completed for {ticker}: {sentiment_score}")
            return True

        except Exception as e:
            logger.error(f"Error checking sentiment limit for {ticker}: {e}")
            return False

    def _update_training_queue_status(self, queue_id: int, mode: str, hft_increment: bool = False, daily_complete: bool = False):
        """Update training queue status"""
        try:
            updates = ["training_mode = %s", "last_processed_at = CURRENT_TIMESTAMP"]
            params = [mode]

            if hft_increment:
                updates.append("hft_predictions_today = hft_predictions_today + 1")

            if daily_complete:
                updates.append("daily_prediction_completed = TRUE")

            query = f"UPDATE training_queue SET {', '.join(updates)} WHERE id = %s"
            params.append(queue_id)

            self.db_service.execute_query(query, params)

        except Exception as e:
            logger.error(f"Error updating training queue status: {e}")

    def _schedule_hft_backtest(self, prediction: Dict, ticker: str, company_id: int):
        """Schedule HFT backtest for 5 minutes later"""
        try:
            # For now, we'll use a simple threading approach
            # In production, you might want to use a proper task queue like Celery

            def delayed_backtest():
                time.sleep(300)  # Wait 5 minutes
                try:
                    # Get current price for comparison
                    import yfinance as yf
                    stock = yf.Ticker(ticker)
                    current_data = stock.history(period="1d", interval="1m")

                    if not current_data.empty:
                        current_price = current_data['Close'].iloc[-1]
                        predicted_price = prediction.get('predicted_price', 0)

                        # Calculate accuracy
                        if predicted_price > 0:
                            accuracy = 100 - abs((current_price - predicted_price) / predicted_price * 100)
                            accuracy = max(0, min(100, accuracy))  # Clamp between 0-100

                            # Store backtest result
                            self.backtesting_service.store_backtest_result(
                                ticker, prediction, current_price, accuracy, is_hft=True
                            )

                            logger.info(f"⚡ HFT Backtest completed for {ticker}: {accuracy:.1f}% accuracy")

                except Exception as e:
                    logger.error(f"Error in HFT backtest for {ticker}: {e}")

            # Start backtest thread
            backtest_thread = threading.Thread(target=delayed_backtest, daemon=True)
            backtest_thread.start()

        except Exception as e:
            logger.error(f"Error scheduling HFT backtest for {ticker}: {e}")

    def start_hft_training(self):
        """Start HFT training mode (5-minute predictions during market hours)"""
        if not self.get_config('enable_hft_mode', True):
            logger.info("⚡ HFT mode disabled in configuration")
            return False

        if self.hft_thread and self.hft_thread.is_alive():
            logger.warning("⚡ HFT training already running")
            return False

        try:
            self.is_running = True
            self.hft_thread = threading.Thread(target=self._hft_training_loop, daemon=True)
            self.hft_thread.start()

            logger.info("⚡ HFT training mode started")
            return True

        except Exception as e:
            logger.error(f"Error starting HFT training: {e}")
            return False

    def start_daily_training(self):
        """Start daily training mode (once after market close)"""
        if not self.get_config('enable_daily_mode', True):
            logger.info("📅 Daily mode disabled in configuration")
            return False

        if self.daily_thread and self.daily_thread.is_alive():
            logger.warning("📅 Daily training already running")
            return False

        try:
            self.daily_thread = threading.Thread(target=self._daily_training_loop, daemon=True)
            self.daily_thread.start()

            logger.info("📅 Daily training mode started")
            return True

        except Exception as e:
            logger.error(f"Error starting daily training: {e}")
            return False

    def _hft_training_loop(self):
        """HFT training loop - runs during market hours"""
        logger.info("⚡ Starting HFT training loop")
        interval_minutes = self.get_config('hft_prediction_interval_minutes', 5)

        while self.is_running:
            try:
                # Check if market is open
                if not self.is_market_open():
                    logger.info("⚡ Market closed - HFT training paused")
                    time.sleep(60)  # Check every minute when market is closed
                    continue

                # Update training status
                self._update_training_status(hft_active=True)

                # Get next batch for HFT processing
                batch = self.get_next_hft_batch()

                if not batch:
                    logger.info("⚡ No stocks available for HFT training")
                    time.sleep(interval_minutes * 60)
                    continue

                logger.info(f"⚡ Processing HFT batch: {len(batch)} stocks")

                # Process each stock in batch
                successful = 0
                for stock in batch:
                    if self.process_hft_prediction(stock):
                        successful += 1

                    # Small delay between stocks to avoid rate limiting
                    time.sleep(1)

                logger.info(f"⚡ HFT batch completed: {successful}/{len(batch)} successful")

                # Update training status
                self._update_training_status(hft_processed=successful)

                # Wait for next interval
                time.sleep(interval_minutes * 60)

            except Exception as e:
                logger.error(f"Error in HFT training loop: {e}")
                time.sleep(60)  # Wait before retrying

        logger.info("⚡ HFT training loop stopped")

    def _daily_training_loop(self):
        """Daily training loop - runs once after market close"""
        logger.info("📅 Starting daily training loop")

        while self.is_running:
            try:
                # Wait for market to close
                if self.is_market_open():
                    logger.info("📅 Waiting for market to close...")
                    time.sleep(300)  # Check every 5 minutes
                    continue

                # Check if it's time for daily training
                daily_start_time = self.get_config('daily_mode_start_time', '16:30')
                now = datetime.now(self.market_tz)
                start_time = dt_time(*map(int, daily_start_time.split(':')))

                if now.time() < start_time:
                    logger.info(f"📅 Waiting for daily training time: {daily_start_time}")
                    time.sleep(300)
                    continue

                # Check if daily training already completed today
                if self._is_daily_training_completed():
                    logger.info("📅 Daily training already completed today")
                    time.sleep(3600)  # Check again in 1 hour
                    continue

                logger.info("📅 Starting daily training session")

                # Update training status
                self._update_training_status(daily_active=True)

                # Process all remaining stocks
                total_processed = 0
                while True:
                    batch = self.get_daily_batch()

                    if not batch:
                        logger.info("📅 No more stocks for daily training")
                        break

                    logger.info(f"📅 Processing daily batch: {len(batch)} stocks")

                    successful = 0
                    for stock in batch:
                        if self.process_daily_prediction(stock):
                            successful += 1

                        # Small delay between stocks
                        time.sleep(2)

                    total_processed += successful
                    logger.info(f"📅 Daily batch completed: {successful}/{len(batch)} successful")

                    # Update progress
                    self._update_training_status(daily_processed=successful)

                # Mark daily training as completed
                self._mark_daily_training_completed()

                logger.info(f"📅 Daily training session completed: {total_processed} stocks processed")

                # Sleep until next day
                time.sleep(3600)  # Check again in 1 hour

            except Exception as e:
                logger.error(f"Error in daily training loop: {e}")
                time.sleep(300)  # Wait before retrying

        logger.info("📅 Daily training loop stopped")

    def _update_training_status(self, hft_active: bool = None, daily_active: bool = None,
                               hft_processed: int = 0, daily_processed: int = 0):
        """Update training status in database"""
        try:
            today = datetime.now().date()

            updates = []
            params = []

            if hft_active is not None:
                updates.append("hft_mode_active = %s")
                params.append(hft_active)

            if daily_active is not None:
                updates.append("daily_mode_active = %s")
                params.append(daily_active)

            if hft_processed > 0:
                updates.append("hft_stocks_processed = hft_stocks_processed + %s")
                params.append(hft_processed)

            if daily_processed > 0:
                updates.append("daily_stocks_processed = daily_stocks_processed + %s")
                params.append(daily_processed)

            if updates:
                updates.append("updated_at = CURRENT_TIMESTAMP")
                query = f"""
                    UPDATE training_status
                    SET {', '.join(updates)}
                    WHERE training_date = %s
                """
                params.append(today)

                self.db_service.execute_query(query, params)

        except Exception as e:
            logger.error(f"Error updating training status: {e}")

    def _is_daily_training_completed(self) -> bool:
        """Check if daily training is already completed for today"""
        try:
            today = datetime.now().date()

            query = """
                SELECT training_cycle_completed
                FROM training_status
                WHERE training_date = %s
            """
            result = self.db_service.execute_query(query, (today,), fetch=True)

            return result and result[0]['training_cycle_completed']

        except Exception as e:
            logger.error(f"Error checking daily training completion: {e}")
            return False

    def _mark_daily_training_completed(self):
        """Mark daily training as completed"""
        try:
            today = datetime.now().date()

            query = """
                UPDATE training_status
                SET training_cycle_completed = TRUE,
                    daily_mode_active = FALSE,
                    updated_at = CURRENT_TIMESTAMP
                WHERE training_date = %s
            """
            self.db_service.execute_query(query, (today,))

            logger.info("✅ Daily training cycle marked as completed")

        except Exception as e:
            logger.error(f"Error marking daily training completed: {e}")

    def start_autonomous_training(self):
        """Start complete autonomous training system"""
        try:
            logger.info("🤖 Starting autonomous training system...")

            # Update stock list first
            self.update_stock_list()

            # Start both training modes
            hft_started = self.start_hft_training()
            daily_started = self.start_daily_training()

            if hft_started or daily_started:
                logger.info("🤖 Autonomous training system started successfully")
                return True
            else:
                logger.error("❌ Failed to start autonomous training system")
                return False

        except Exception as e:
            logger.error(f"Error starting autonomous training: {e}")
            return False

    def stop_autonomous_training(self):
        """Stop autonomous training system"""
        try:
            logger.info("🛑 Stopping autonomous training system...")

            self.is_running = False

            # Wait for threads to finish
            if self.hft_thread and self.hft_thread.is_alive():
                self.hft_thread.join(timeout=10)

            if self.daily_thread and self.daily_thread.is_alive():
                self.daily_thread.join(timeout=10)

            # Update training status
            today = datetime.now().date()
            self.db_service.execute_query(
                """UPDATE training_status
                   SET hft_mode_active = FALSE, daily_mode_active = FALSE,
                       updated_at = CURRENT_TIMESTAMP
                   WHERE training_date = %s""",
                (today,)
            )

            logger.info("🛑 Autonomous training system stopped")
            return True

        except Exception as e:
            logger.error(f"Error stopping autonomous training: {e}")
            return False

    def get_training_progress(self) -> Dict:
        """Get current training progress"""
        try:
            today = datetime.now().date()

            # Get training status
            status_query = """
                SELECT * FROM training_status
                WHERE training_date = %s
            """
            status = self.db_service.execute_query(status_query, (today,), fetch=True)

            if not status:
                return {
                    'date': today.isoformat(),
                    'total_stocks': 0,
                    'progress': 0,
                    'status': 'not_started'
                }

            status = status[0]

            # Get queue statistics
            queue_query = """
                SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN training_mode = 'completed' THEN 1 END) as completed,
                    COUNT(CASE WHEN training_mode = 'hft' THEN 1 END) as hft_active,
                    COUNT(CASE WHEN training_mode = 'daily' THEN 1 END) as daily_active,
                    COUNT(CASE WHEN training_mode = 'pending' THEN 1 END) as pending
                FROM training_queue
            """
            queue_stats = self.db_service.execute_query(queue_query, fetch=True)

            if queue_stats:
                queue_stats = queue_stats[0]
                total = queue_stats['total']
                completed = queue_stats['completed']
                progress = (completed / total * 100) if total > 0 else 0
            else:
                total = completed = progress = 0
                queue_stats = {}

            return {
                'date': today.isoformat(),
                'total_stocks': status['total_stocks'],
                'hft_stocks_processed': status['hft_stocks_processed'],
                'daily_stocks_processed': status['daily_stocks_processed'],
                'completed_stocks': completed,
                'progress_percent': round(progress, 2),
                'hft_mode_active': status['hft_mode_active'],
                'daily_mode_active': status['daily_mode_active'],
                'training_cycle_completed': status['training_cycle_completed'],
                'market_open': self.is_market_open(),
                'queue_stats': queue_stats
            }

        except Exception as e:
            logger.error(f"Error getting training progress: {e}")
            return {'error': str(e)}

    def reset_daily_training(self):
        """Reset training for new day"""
        try:
            logger.info("🔄 Resetting training for new day...")

            # Update stock list for IPOs/delistings
            self.update_stock_list()

            # Reset training queue
            self.db_service.execute_query("""
                UPDATE training_queue SET
                    training_mode = 'pending',
                    sentiment_analyzed_today = FALSE,
                    hft_predictions_today = 0,
                    daily_prediction_completed = FALSE,
                    last_processed_at = NULL,
                    updated_at = CURRENT_TIMESTAMP
            """)

            # Clear old sentiment tracking
            yesterday = datetime.now().date() - timedelta(days=1)
            self.db_service.execute_query(
                "DELETE FROM sentiment_tracking WHERE sentiment_date < %s",
                (yesterday,)
            )

            logger.info("✅ Training reset completed")
            return True

        except Exception as e:
            logger.error(f"Error resetting daily training: {e}")
            return False

    def _is_training_cycle_complete(self) -> bool:
        """Check if training cycle is complete for today"""
        try:
            # Check if all stocks have been processed
            incomplete_query = """
                SELECT COUNT(*) FROM training_queue
                WHERE training_mode != 'completed'
            """
            result = self.db_service.execute_query(incomplete_query, fetch=True)

            if result and result[0]['count'] == 0:
                # All stocks completed, update training status
                today = datetime.now().date()
                self.db_service.execute_query("""
                    UPDATE training_status
                    SET training_cycle_completed = TRUE,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE training_date = %s
                """, (today,))

                return True

            return False

        except Exception as e:
            logger.error(f"Error checking training completion: {e}")
            return False

# Convenience functions for external use
def start_autonomous_training():
    """Start autonomous training system"""
    manager = AutonomousTrainingManager()
    return manager.start_autonomous_training()

def get_training_progress():
    """Get current training progress"""
    manager = AutonomousTrainingManager()
    return manager.get_training_progress()

def update_stock_list():
    """Update stock list for IPOs/delistings"""
    manager = AutonomousTrainingManager()
    return manager.update_stock_list()

if __name__ == "__main__":
    # Test the autonomous training manager
    manager = AutonomousTrainingManager()

    # Update stock list
    manager.update_stock_list()

    # Start autonomous training
    manager.start_autonomous_training()

    try:
        # Keep running
        while True:
            progress = manager.get_training_progress()
            logger.info(f"📊 Training Progress: {progress['progress_percent']:.1f}% complete")
            time.sleep(300)  # Report every 5 minutes

    except KeyboardInterrupt:
        logger.info("🛑 Shutting down autonomous training...")
        manager.stop_autonomous_training()
