"""
StockTrek ETL Service
Enhanced ETL service for neural network integration
Handles data flow between Preprocessing, Neural Network, and Database
"""

import psycopg2
import json
import logging
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional
from Preprocessing.financial import fetch_data, process_data
from Preprocessing.sentiment import SentimentAnalyzer, get_company_info_from_ticker
import math

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

DB_NAME = 'stocktrek_mainbranch'
DB_USER = 'stocktrek_admin'
DB_PASSWORD = 'equity_FR'
DB_HOST = 'localhost'
DB_PORT = '5432'


class ETLService:
    """
    Enhanced ETL Service for StockTrek
    Bridges Preprocessing, Neural Network, and Database components
    """

    def __init__(self):
        """Initialize ETL service"""
        self.db_config = {
            'dbname': DB_NAME,
            'user': DB_USER,
            'password': DB_PASSWORD,
            'host': DB_HOST,
            'port': DB_PORT
        }

    def get_comprehensive_stock_data(self, ticker: str) -> Optional[Dict]:
        """Get comprehensive stock data for neural network prediction"""
        try:
            logger.info(f"Getting comprehensive data for {ticker}")

            # Get company name
            company_name = get_company_info_from_ticker(ticker)

            # Fetch financial data via preprocessing
            raw_financial = fetch_data(ticker)
            if not raw_financial:
                logger.warning(f"No financial data available for {ticker}")
                return None

            financials = process_data(raw_financial)

            # Get sentiment analysis
            analyzer = SentimentAnalyzer()
            sentiment_result = analyzer.get_company_sentiment_analysis(company_name, ticker, days_back=7)

            # Combine all data for neural network
            comprehensive_data = {
                'symbol': ticker,
                'company_name': company_name,
                **financials,
                'sentiment_score': sentiment_result.get('overall_sentiment_score', 0.0),
                'news_count': sentiment_result.get('total_articles', 0),
                'sentiment_data': sentiment_result,
                'data_quality': self._assess_data_quality(financials),
                'timestamp': datetime.now().isoformat()
            }

            return comprehensive_data

        except Exception as e:
            logger.error(f"Error getting comprehensive data for {ticker}: {e}")
            return None

    def _assess_data_quality(self, data: Dict) -> str:
        """Assess the quality of the data for neural network prediction"""
        try:
            required_fields = ['price', 'volume', 'market_cap', 'pe_ratio']
            optional_fields = ['eps', 'dividend_yield', 'beta', 'moving_avg_50', 'rsi_14']

            missing_required = [field for field in required_fields if not data.get(field)]
            missing_optional = [field for field in optional_fields if not data.get(field)]

            if not missing_required and len(missing_optional) <= 1:
                return 'excellent'
            elif not missing_required and len(missing_optional) <= 3:
                return 'good'
            elif len(missing_required) <= 1:
                return 'basic'
            elif len(missing_required) <= 2:
                return 'minimal'
            else:
                return 'emergency_fallback'

        except Exception as e:
            logger.error(f"Error assessing data quality: {e}")
            return 'unknown'

    def store_prediction(self, ticker: str, prediction: Dict, timeframe_days: int) -> bool:
        """Store neural network prediction in database"""
        try:
            # Import database service
            from Databases.database_service import DatabaseService
            db_service = DatabaseService()

            # Get or create company
            company = db_service.get_company_by_symbol(ticker)
            if not company:
                company_name = get_company_info_from_ticker(ticker)
                company_id = db_service.insert_company(company_name, ticker)
            else:
                company_id = company['id']

            if not company_id:
                logger.error(f"Could not get/create company for {ticker}")
                return False

            # Prepare prediction data
            prediction_data = {
                'prediction': prediction.get('prediction', 'UNKNOWN'),
                'confidence': prediction.get('confidence', 0),
                'current_price': prediction.get('current_price'),
                'predicted_price': prediction.get('predicted_price'),
                'price_change': prediction.get('price_change'),
                'price_change_percent': prediction.get('price_change_percent'),
                'timeframe_days': timeframe_days,
                'model_version': prediction.get('model_version', 'production_v1'),
                'data_quality': prediction.get('data_quality', 'unknown')
            }

            # Store prediction
            success = db_service.store_prediction(company_id, prediction_data)

            if success:
                logger.info(f"Stored prediction for {ticker}: {prediction['prediction']} ({prediction['confidence']}%)")
            else:
                logger.error(f"Failed to store prediction for {ticker}")

            return success

        except Exception as e:
            logger.error(f"Error storing prediction for {ticker}: {e}")
            return False

def clean_for_json(val):
    if isinstance(val, float) and (math.isnan(val) or math.isinf(val)):
        return None
    if isinstance(val, list):
        return [clean_for_json(x) for x in val]
    if isinstance(val, dict):
        return {k: clean_for_json(v) for k, v in val.items()}
    return val

def to_jsonb(val):
    if val is None:
        return None
    cleaned = clean_for_json(val)
    return json.dumps(cleaned)

def etl_for_ticker(ticker):
    company_name = get_company_info_from_ticker(ticker)
    print(f"Processing {company_name} ({ticker})")

    raw_financial = fetch_data(ticker)
    financials = process_data(raw_financial)

    analyzer = SentimentAnalyzer()
    sentiment_result = analyzer.get_company_sentiment_analysis(company_name, ticker, days_back=7)
    sentiment_json = json.dumps(sentiment_result)

    conn = psycopg2.connect(
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD,
        host=DB_HOST,
        port=DB_PORT
    )
    cur = conn.cursor()

    cur.execute("SELECT id FROM companies WHERE ticker = %s", (ticker,))
    result = cur.fetchone()
    if result:
        company_id = result[0]
    else:
        cur.execute("INSERT INTO companies (name, ticker) VALUES (%s, %s) RETURNING id", (company_name, ticker))
        company_id = cur.fetchone()[0]
        conn.commit()

    company_data = {
        'company_id': company_id,
        'revenue': to_jsonb(financials.get('revenue_5y')),
        'revenue_growth_rate_fwd': None, 
        'revenue_growth_rate_trailing': None,
        'ebitda': to_jsonb(financials.get('ebitda_5y')),
        'ebitda_growth_rate_fwd': None, 
        'ebitda_growth_rate_trailing': None, 
        'depreciation_amortization': to_jsonb(financials.get('depreciation_amortization_5y')),
        'ebit': to_jsonb(financials.get('ebit')),
        'capex': to_jsonb(financials.get('capex')),
        'working_capital': to_jsonb(financials.get('working_capital')),
        'tax_rate': financials.get('tax_rate'),
        'levered_fcf': to_jsonb(financials.get('levered_fcf')),
        'wacc': financials.get('wacc'),
        'debt_to_equity_ratio': financials.get('debt_to_equity_ratio'),
        'current_ratio': financials.get('current_ratio'),
        'quick_ratio': financials.get('quick_ratio'),
        'gross_profit_margin': financials.get('gross_profit_margin'),
        'pe_ratio': financials.get('pe_ratio'),
        'eps': financials.get('eps'),
        'ps_ratio': financials.get('ps_ratio'),
        'dividend_yield_percentage': financials.get('dividend_yield_percentage'),
        'ev_to_ebitda': financials.get('ev_ebitda'),
        'net_income': to_jsonb(financials.get('net_income')),
        'sentiment_data': sentiment_json,
        'as_of_date': date.today()
    }

    columns = ', '.join(company_data.keys())
    placeholders = ', '.join(['%s'] * len(company_data))
    values = list(company_data.values())
    cur.execute(f"INSERT INTO company_data ({columns}) VALUES ({placeholders})", values)
    conn.commit()
    print(f"Inserted data for {company_name} ({ticker})")
    cur.close()
    conn.close() 