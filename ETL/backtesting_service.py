"""
StockTrek Backtesting Service
Evaluates predictions and provides feedback to the neural network
"""

import sys
import os
import logging
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BacktestingService:
    """
    Backtesting service that evaluates predictions and provides feedback
    """
    
    def __init__(self):
        """Initialize the backtesting service"""
        self.database = DatabaseService()
        logger.info("Backtesting service initialized")
    
    def get_actual_price_movement(self, symbol: str, start_date: datetime, 
                                end_date: datetime) -> Optional[Dict]:
        """Get actual price movement for a stock between dates"""
        try:
            # Fetch stock data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(start=start_date.date(), end=end_date.date())
            
            if hist.empty or len(hist) < 2:
                logger.warning(f"Insufficient price data for {symbol}")
                return None
            
            start_price = hist['Close'].iloc[0]
            end_price = hist['Close'].iloc[-1]
            
            price_change = end_price - start_price
            price_change_percent = (price_change / start_price) * 100
            
            # Determine movement direction
            if price_change_percent > 5:
                movement = 'STRONG_BUY'
            elif price_change_percent > 1:
                movement = 'BUY'
            elif price_change_percent < -5:
                movement = 'STRONG_SELL'
            elif price_change_percent < -1:
                movement = 'SELL'
            else:
                movement = 'HOLD'
            
            return {
                'start_price': float(start_price),
                'end_price': float(end_price),
                'price_change': float(price_change),
                'price_change_percent': float(price_change_percent),
                'movement': movement
            }
            
        except Exception as e:
            logger.error(f"Error getting price movement for {symbol}: {e}")
            return None
    
    def calculate_prediction_accuracy(self, predicted: str, actual: str) -> float:
        """Calculate accuracy score for a prediction"""
        try:
            # Exact match gets full score
            if predicted == actual:
                return 1.0
            
            # Partial matches for directional accuracy
            buy_predictions = ['BUY', 'STRONG_BUY']
            sell_predictions = ['SELL', 'STRONG_SELL']
            
            predicted_direction = 'up' if predicted in buy_predictions else 'down' if predicted in sell_predictions else 'hold'
            actual_direction = 'up' if actual in buy_predictions else 'down' if actual in sell_predictions else 'hold'
            
            if predicted_direction == actual_direction:
                return 0.7  # Partial credit for correct direction
            else:
                return 0.0  # No credit for wrong direction
                
        except Exception as e:
            logger.error(f"Error calculating accuracy: {e}")
            return 0.0
    
    def evaluate_prediction(self, prediction: Dict) -> Optional[Dict]:
        """Evaluate a single prediction against actual market data"""
        try:
            symbol = prediction['symbol']
            prediction_date = prediction['prediction_date']
            target_date = prediction['target_date']
            predicted_outcome = prediction['prediction_type']
            
            # Get actual price movement
            actual_data = self.get_actual_price_movement(symbol, prediction_date, target_date)
            
            if not actual_data:
                logger.warning(f"Could not get actual data for {symbol}")
                return None
            
            actual_outcome = actual_data['movement']
            accuracy_score = self.calculate_prediction_accuracy(predicted_outcome, actual_outcome)
            
            # Update prediction in database
            success = self.database.update_prediction_outcome(
                prediction['id'], actual_outcome, accuracy_score
            )
            
            if not success:
                logger.error(f"Failed to update prediction {prediction['id']}")
                return None
            
            evaluation_result = {
                'prediction_id': prediction['id'],
                'symbol': symbol,
                'predicted': predicted_outcome,
                'actual': actual_outcome,
                'accuracy_score': accuracy_score,
                'price_change_percent': actual_data['price_change_percent'],
                'evaluation_date': datetime.now().isoformat()
            }
            
            logger.info(f"Evaluated {symbol}: {predicted_outcome} vs {actual_outcome} (accuracy: {accuracy_score:.2f})")
            
            return evaluation_result
            
        except Exception as e:
            logger.error(f"Error evaluating prediction: {e}")
            return None
    
    def evaluate_matured_predictions(self) -> List[Dict]:
        """Evaluate predictions that have reached their target timeframe"""
        try:
            # Get predictions that are ready for evaluation
            matured_predictions = self.get_matured_predictions()
            results = []

            for prediction in matured_predictions:
                result = self.evaluate_single_prediction(prediction)
                if result:
                    results.append(result)
                    # Update prediction with actual outcome
                    self.update_prediction_outcome(prediction['id'], result)
                    # Trigger learning from this result
                    self.trigger_learning_update(prediction, result)

            return results

        except Exception as e:
            logger.error(f"Error evaluating matured predictions: {e}")
            return []

    def get_matured_predictions(self) -> List[Dict]:
        """Get predictions that have reached their evaluation timeframe"""
        try:
            query = """
                SELECT p.*, c.symbol, c.name
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.target_date <= %s
                AND p.actual_outcome IS NULL
                AND p.created_at >= %s
                ORDER BY p.target_date ASC
            """

            # Look for predictions from last 60 days that are now mature
            cutoff_date = datetime.now() - timedelta(days=60)
            params = (datetime.now().date(), cutoff_date)

            results = self.database.execute_query(query, params, fetch=True)
            return results if results else []

        except Exception as e:
            logger.error(f"Error getting matured predictions: {e}")
            return []

    def evaluate_single_prediction(self, prediction: Dict) -> Optional[Dict]:
        """Evaluate a single matured prediction"""
        try:
            symbol = prediction['symbol']
            target_date = prediction['target_date']
            predicted_price = prediction.get('predicted_price')
            current_price = prediction.get('current_price')

            # Get actual price on target date
            actual_price = self.get_actual_price_on_date(symbol, target_date)

            if actual_price is None:
                logger.warning(f"Could not get actual price for {symbol} on {target_date}")
                return None

            # Calculate metrics
            if predicted_price and current_price:
                price_error = abs(predicted_price - actual_price) / actual_price
                direction_correct = self.check_direction_accuracy_detailed(
                    prediction, actual_price
                )

                return {
                    'prediction_id': prediction['id'],
                    'symbol': symbol,
                    'predicted_price': predicted_price,
                    'actual_price': actual_price,
                    'current_price': current_price,
                    'price_error': price_error,
                    'direction_correct': direction_correct,
                    'accuracy_score': 1.0 - price_error if direction_correct else 0.5 - price_error,
                    'evaluation_date': datetime.now()
                }

            return None

        except Exception as e:
            logger.error(f"Error evaluating single prediction: {e}")
            return None

    def get_actual_price_on_date(self, symbol: str, target_date) -> Optional[float]:
        """Get the actual stock price on a specific date"""
        try:
            ticker = yf.Ticker(symbol)

            # Get data around the target date
            start_date = target_date - timedelta(days=3)
            end_date = target_date + timedelta(days=3)

            hist = ticker.history(start=start_date, end=end_date)

            if hist.empty:
                return None

            # Try to get exact date, or closest available
            if target_date.strftime('%Y-%m-%d') in hist.index.strftime('%Y-%m-%d'):
                return float(hist.loc[hist.index.strftime('%Y-%m-%d') == target_date.strftime('%Y-%m-%d'), 'Close'].iloc[0])
            else:
                # Get closest date
                return float(hist['Close'].iloc[-1])

        except Exception as e:
            logger.error(f"Error getting actual price for {symbol}: {e}")
            return None

    def check_direction_accuracy_detailed(self, prediction: Dict, actual_price: float) -> bool:
        """Check if the predicted direction was correct"""
        try:
            predicted_direction = prediction.get('prediction_type', '').upper()
            current_price = prediction.get('current_price', 0)

            if current_price <= 0 or actual_price <= 0:
                return False

            price_change_percent = ((actual_price - current_price) / current_price) * 100

            # Determine actual direction based on price change
            if abs(price_change_percent) < 2:
                actual_direction = "HOLD"
            elif price_change_percent > 0:
                actual_direction = "BUY"
            else:
                actual_direction = "SELL"

            return predicted_direction == actual_direction

        except Exception as e:
            logger.error(f"Error checking direction accuracy: {e}")
            return False

    def update_prediction_outcome(self, prediction_id: int, result: Dict):
        """Update prediction with actual outcome"""
        try:
            query = """
                UPDATE neural_network_predictions
                SET actual_outcome = %s,
                    actual_price = %s,
                    accuracy_score = %s,
                    evaluated_at = %s
                WHERE id = %s
            """

            outcome = "BUY" if result['direction_correct'] and result['actual_price'] > result['current_price'] else \
                     "SELL" if result['direction_correct'] and result['actual_price'] < result['current_price'] else \
                     "HOLD"

            params = (
                outcome,
                result['actual_price'],
                result['accuracy_score'],
                datetime.now(),
                prediction_id
            )

            self.database.execute_query(query, params)
            logger.info(f"Updated prediction {prediction_id} with outcome: {outcome}")

        except Exception as e:
            logger.error(f"Error updating prediction outcome: {e}")

    def trigger_learning_update(self, prediction: Dict, result: Dict):
        """Trigger neural network learning from prediction result"""
        try:
            # Calculate learning metrics
            predicted_price = prediction.get('predicted_price', 0)
            actual_price = result.get('actual_price', 0)

            if predicted_price > 0 and actual_price > 0:
                # Create learning feedback
                learning_data = {
                    'prediction_id': prediction['id'],
                    'predicted_price': predicted_price,
                    'actual_price': actual_price,
                    'price_error': result['price_error'],
                    'direction_correct': result['direction_correct'],
                    'confidence': prediction.get('confidence', 0),
                    'timeframe': prediction.get('timeframe_days', 7),
                    'symbol': prediction.get('symbol', ''),
                    'prediction_date': prediction.get('created_at'),
                    'evaluation_date': datetime.now()
                }

                # Store learning data for batch processing
                self.store_learning_data(learning_data)

                logger.info(f"Learning data stored for {prediction.get('symbol', 'Unknown')} - "
                          f"Price Error: {result['price_error']:.2%}, Direction: {'✓' if result['direction_correct'] else '✗'}")

        except Exception as e:
            logger.error(f"Error triggering learning update: {e}")

    def store_learning_data(self, learning_data: Dict):
        """Store learning data for neural network training"""
        try:
            query = """
                INSERT INTO learning_feedback (
                    prediction_id, predicted_price, actual_price, price_error,
                    direction_correct, confidence, timeframe_days, symbol,
                    prediction_date, evaluation_date, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """

            params = (
                learning_data['prediction_id'],
                learning_data['predicted_price'],
                learning_data['actual_price'],
                learning_data['price_error'],
                learning_data['direction_correct'],
                learning_data['confidence'],
                learning_data['timeframe'],
                learning_data['symbol'],
                learning_data['prediction_date'],
                learning_data['evaluation_date'],
                datetime.now()
            )

            self.database.execute_query(query, params)
            logger.info(f"Learning data stored for prediction {learning_data['prediction_id']}")

        except Exception as e:
            logger.error(f"Error storing learning data: {e}")

    def run_evaluation_cycle(self) -> Dict:
        """Run a complete evaluation cycle for all ready predictions"""
        try:
            logger.info("Starting evaluation cycle...")
            
            # Get predictions ready for evaluation
            predictions = self.database.get_predictions_for_evaluation()
            
            if not predictions:
                logger.info("No predictions ready for evaluation")
                return {
                    'status': 'completed',
                    'predictions_evaluated': 0,
                    'evaluations': [],
                    'timestamp': datetime.now().isoformat()
                }
            
            logger.info(f"Found {len(predictions)} predictions ready for evaluation")
            
            evaluations = []
            successful_evaluations = 0
            
            for prediction in predictions:
                try:
                    evaluation = self.evaluate_prediction(prediction)
                    if evaluation:
                        evaluations.append(evaluation)
                        successful_evaluations += 1
                except Exception as e:
                    logger.error(f"Error evaluating prediction {prediction.get('id', 'unknown')}: {e}")
            
            # Calculate summary statistics
            if evaluations:
                total_accuracy = sum(eval['accuracy_score'] for eval in evaluations)
                average_accuracy = total_accuracy / len(evaluations)
            else:
                average_accuracy = 0
            
            results = {
                'status': 'completed',
                'predictions_found': len(predictions),
                'predictions_evaluated': successful_evaluations,
                'average_accuracy': round(average_accuracy, 3),
                'evaluations': evaluations,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Evaluation cycle completed: {successful_evaluations}/{len(predictions)} evaluated")
            logger.info(f"Average accuracy: {average_accuracy:.3f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in evaluation cycle: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_performance_metrics(self, days_back: int = 30) -> Dict:
        """Get performance metrics for recent predictions"""
        try:
            # Get recent predictions with outcomes
            query = """
                SELECT prediction_type, actual_outcome, accuracy_score, 
                       prediction_date, target_date
                FROM neural_network_predictions 
                WHERE prediction_date >= %s 
                AND actual_outcome IS NOT NULL
                ORDER BY prediction_date DESC
            """
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            predictions = self.database.execute_query(query, (cutoff_date,), fetch=True)
            
            if not predictions:
                return {
                    'total_predictions': 0,
                    'average_accuracy': 0,
                    'prediction_breakdown': {},
                    'timestamp': datetime.now().isoformat()
                }
            
            # Calculate metrics
            total_predictions = len(predictions)
            total_accuracy = sum(p['accuracy_score'] for p in predictions)
            average_accuracy = total_accuracy / total_predictions
            
            # Breakdown by prediction type
            breakdown = {}
            for pred in predictions:
                pred_type = pred['prediction_type']
                if pred_type not in breakdown:
                    breakdown[pred_type] = {'count': 0, 'total_accuracy': 0}
                
                breakdown[pred_type]['count'] += 1
                breakdown[pred_type]['total_accuracy'] += pred['accuracy_score']
            
            # Calculate averages for each type
            for pred_type in breakdown:
                count = breakdown[pred_type]['count']
                breakdown[pred_type]['average_accuracy'] = breakdown[pred_type]['total_accuracy'] / count
            
            return {
                'total_predictions': total_predictions,
                'average_accuracy': round(average_accuracy, 3),
                'prediction_breakdown': breakdown,
                'days_analyzed': days_back,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def cleanup(self):
        """Clean up backtesting service resources"""
        try:
            self.database.close_connection()
            logger.info("Backtesting service cleaned up")
        except Exception as e:
            logger.error(f"Error during backtesting cleanup: {e}")
