"""
StockTrek Autonomous Market Scanner
Automatically scans markets and makes predictions
"""

import sys
import os
import logging
import time
import threading
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict
import pytz

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from ETL.prediction_service import PredictionService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AutonomousScanner:
    """
    Autonomous market scanner that continuously monitors and predicts stocks
    """
    
    def __init__(self):
        """Initialize the autonomous scanner"""
        self.prediction_service = PredictionService()
        self.is_running = False
        self.scan_thread = None
        self.scan_interval = 300  # 5 minutes for HFT mode

        # Multi-timeframe prediction system
        self.prediction_timeframes = [1, 5, 10, 30]  # Days as requested

        # Efficient ticker management
        self.all_tickers = []
        self.nasdaq_tickers = []
        self.nyse_tickers = []
        self.amex_tickers = []
        self.current_ticker_index = 0  # For efficient one-at-a-time processing
        self.last_ticker_update = None  # Track when we last updated the list

        # Market timezone
        self.market_tz = pytz.timezone('US/Eastern')

        # Load ticker lists efficiently (only once per day)
        self._load_tickers_if_needed()

        logger.info(f"Autonomous scanner initialized with {len(self.all_tickers)} total tickers")
        logger.info(f"📊 NASDAQ: {len(self.nasdaq_tickers)}, NYSE: {len(self.nyse_tickers)}, AMEX: {len(self.amex_tickers)}")
        logger.info(f"⚡ HFT Mode: {self.scan_interval} second intervals")
        logger.info(f"🔄 Efficient one-at-a-time processing enabled")

    def _load_tickers_if_needed(self):
        """Load ticker lists only if needed (once per day or if empty)"""
        now = datetime.now()

        # Check if we need to update (once per day or if empty)
        needs_update = (
            not self.all_tickers or  # Empty list
            not self.last_ticker_update or  # Never updated
            (now - self.last_ticker_update).days >= 1  # More than 1 day old
        )

        if needs_update:
            logger.info("📅 Daily ticker list update needed - loading comprehensive lists...")
            self._load_comprehensive_tickers()
            self.last_ticker_update = now
            logger.info(f"✅ Ticker lists updated - next update: {(now + timedelta(days=1)).strftime('%Y-%m-%d %H:%M')}")
        else:
            logger.info(f"✅ Using cached ticker lists ({len(self.all_tickers)} tickers) - next update: {(self.last_ticker_update + timedelta(days=1)).strftime('%Y-%m-%d %H:%M')}")

    def _load_comprehensive_tickers(self):
        """Load MAXIMUM comprehensive list of ALL securities traded on US markets"""
        try:
            logger.info("🇺🇸 Loading MAXIMUM comprehensive ticker lists from ALL US MARKETS...")

            # Method 1: NASDAQ official lists (ALL NASDAQ-listed securities)
            try:
                nasdaq_url = "https://ftp.nasdaqtrader.com/dynamic/SymDir/nasdaqlisted.txt"
                nasdaq_data = pd.read_csv(nasdaq_url, sep='|')
                # Get ALL symbols, including ETFs, REITs, etc.
                self.nasdaq_tickers = nasdaq_data['Symbol'].dropna().tolist()
                # More permissive filtering - include more symbol types
                self.nasdaq_tickers = [t for t in self.nasdaq_tickers if len(t) <= 6 and not t.startswith('$')]
                logger.info(f"✅ Loaded {len(self.nasdaq_tickers)} NASDAQ securities (stocks, ETFs, REITs)")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load NASDAQ tickers: {e}")
                self.nasdaq_tickers = []

            # Method 2: NYSE and ALL other US exchanges
            try:
                other_url = "https://ftp.nasdaqtrader.com/dynamic/SymDir/otherlisted.txt"
                other_data = pd.read_csv(other_url, sep='|')

                # Get ALL exchanges, not just NYSE and AMEX
                all_exchanges = other_data['Exchange'].unique()
                logger.info(f"📊 Found exchanges: {list(all_exchanges)}")

                # NYSE (N)
                nyse_data = other_data[other_data['Exchange'] == 'N']
                self.nyse_tickers = nyse_data['ACT Symbol'].dropna().tolist()

                # AMEX (A)
                amex_data = other_data[other_data['Exchange'] == 'A']
                self.amex_tickers = amex_data['ACT Symbol'].dropna().tolist()

                # Other US exchanges (P = NYSE Arca, Z = BATS, etc.)
                other_exchanges_data = other_data[~other_data['Exchange'].isin(['N', 'A'])]
                other_tickers = other_exchanges_data['ACT Symbol'].dropna().tolist()

                # Clean up all tickers - more permissive
                self.nyse_tickers = [t for t in self.nyse_tickers if len(t) <= 6 and not t.startswith('$')]
                self.amex_tickers = [t for t in self.amex_tickers if len(t) <= 6 and not t.startswith('$')]
                other_tickers = [t for t in other_tickers if len(t) <= 6 and not t.startswith('$')]

                # Add other exchange tickers to AMEX list
                self.amex_tickers.extend(other_tickers)

                logger.info(f"✅ Loaded {len(self.nyse_tickers)} NYSE securities")
                logger.info(f"✅ Loaded {len(self.amex_tickers)} AMEX + other exchange securities")
            except Exception as e:
                logger.warning(f"⚠️ Failed to load NYSE/AMEX tickers: {e}")
                self.nyse_tickers = []
                self.amex_tickers = []

            # Method 3: S&P 500 + S&P 400 + S&P 600 (ALL S&P indices)
            try:
                sp500_url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
                sp500_data = pd.read_html(sp500_url)[0]
                sp500_tickers = sp500_data['Symbol'].tolist()
                # Clean S&P 500 tickers
                sp500_tickers = [t.replace('.', '-') for t in sp500_tickers if isinstance(t, str)]
                logger.info(f"✅ Loaded {len(sp500_tickers)} S&P 500 tickers")

                # Add to appropriate lists if not already present
                for ticker in sp500_tickers:
                    if ticker not in self.nasdaq_tickers and ticker not in self.nyse_tickers and ticker not in self.amex_tickers:
                        self.nyse_tickers.append(ticker)  # Most S&P 500 are NYSE

            except Exception as e:
                logger.warning(f"⚠️ Failed to load S&P 500 tickers: {e}")

            # Method 4: Russell indices (Russell 1000, 2000, 3000)
            try:
                # Add comprehensive Russell 2000 sample (small caps)
                russell_tickers = [
                    'AACG', 'AADI', 'AAIC', 'AAME', 'AAON', 'AAWW', 'ABCB', 'ABCL', 'ABCM', 'ABEO',
                    'ABIO', 'ABLV', 'ABMD', 'ABOS', 'ABSI', 'ABST', 'ABUS', 'ACAD', 'ACCD', 'ACCO',
                    'ACEL', 'ACER', 'ACES', 'ACET', 'ACGL', 'ACHC', 'ACHL', 'ACHR', 'ACHV', 'ACIA'
                ]
                for ticker in russell_tickers:
                    if ticker not in self.nasdaq_tickers and ticker not in self.nyse_tickers and ticker not in self.amex_tickers:
                        self.nasdaq_tickers.append(ticker)

                logger.info(f"✅ Added {len(russell_tickers)} Russell small cap tickers")
            except Exception as e:
                logger.warning(f"⚠️ Failed to add Russell tickers: {e}")

            # Method 5: Popular penny stocks and micro caps
            try:
                penny_stock_tickers = [
                    'SNDL', 'NAKD', 'GNUS', 'XSPA', 'SHIP', 'TOPS', 'GLBS', 'CTRM', 'SOLO', 'IDEX',
                    'AMC', 'GME', 'BB', 'NOK', 'KOSS', 'WKHS', 'RIDE', 'SPCE', 'TLRY', 'CGC',
                    'ACB', 'CRON', 'HEXO', 'OGI', 'GRWG', 'IIPR', 'BNGO', 'OCGN', 'SENS', 'JAGX'
                ]
                for ticker in penny_stock_tickers:
                    if ticker not in self.nasdaq_tickers and ticker not in self.nyse_tickers and ticker not in self.amex_tickers:
                        self.nasdaq_tickers.append(ticker)

                logger.info(f"✅ Added {len(penny_stock_tickers)} popular penny stock tickers")
            except Exception as e:
                logger.warning(f"⚠️ Failed to add penny stock tickers: {e}")

            # Combine all tickers
            self.all_tickers = list(set(self.nasdaq_tickers + self.nyse_tickers + self.amex_tickers))

            # If we got very few tickers from APIs, use comprehensive fallback
            if len(self.all_tickers) < 100:
                logger.warning(f"⚠️ Only got {len(self.all_tickers)} tickers from APIs, using comprehensive fallback")
                self._load_fallback_tickers()
            else:
                # Add popular penny stocks and small caps manually if not already included
                penny_stocks = [
                    'SNDL', 'NAKD', 'GNUS', 'XSPA', 'SHIP', 'TOPS', 'GLBS', 'CTRM', 'SOLO', 'IDEX',
                    'GEVO', 'PLUG', 'FCEL', 'BIOL', 'BNGO', 'OCGN', 'SENS', 'JAGX', 'EXPR', 'CLOV'
                ]

                for ticker in penny_stocks:
                    if ticker not in self.all_tickers:
                        self.all_tickers.append(ticker)

                # Sort for consistent ordering
                self.all_tickers.sort()

                logger.info(f"🎯 Total comprehensive ticker list: {len(self.all_tickers)} companies")
                logger.info(f"📈 Including penny stocks, small cap, mid cap, and large cap")

        except Exception as e:
            logger.error(f"❌ Failed to load comprehensive tickers: {e}")
            self._load_fallback_tickers()

    def _load_fallback_tickers(self):
        """Load MAXIMUM comprehensive fallback ticker list - ALL US-traded securities"""
        logger.info("🇺🇸 Loading MAXIMUM comprehensive fallback ticker list for ALL US markets...")

        # MAXIMUM comprehensive fallback list - ALL US-traded securities
        self.all_tickers = [
                # COMPLETE S&P 500 (Large Cap)
                'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'META', 'NVDA', 'TSLA', 'BRK-B', 'UNH',
                'JNJ', 'JPM', 'V', 'PG', 'HD', 'MA', 'BAC', 'ABBV', 'PFE', 'KO', 'AVGO', 'PEP',
                'TMO', 'COST', 'MRK', 'ABT', 'ACN', 'NFLX', 'ADBE', 'DHR', 'VZ', 'NKE', 'CRM',
                'LIN', 'TXN', 'WMT', 'NEE', 'RTX', 'QCOM', 'LOW', 'UPS', 'ORCL', 'PM', 'HON',
                'AMGN', 'CVX', 'SBUX', 'T', 'MDT', 'UNP', 'IBM', 'INTU', 'CAT', 'GS', 'AMD',
                'BKNG', 'DE', 'AXP', 'TGT', 'GILD', 'MMM', 'MU', 'ISRG', 'LRCX', 'SYK', 'TJX',
                'AMT', 'MDLZ', 'CVS', 'VRTX', 'ZTS', 'AMAT', 'ADI', 'CI', 'NOW', 'REGN', 'C',
                'PLD', 'SO', 'DUK', 'BSX', 'CB', 'CL', 'FIS', 'MO', 'EOG', 'ITW', 'WM', 'MMC',
                'CSX', 'USB', 'AON', 'GD', 'COP', 'EMR', 'NSC', 'PGR', 'FCX', 'APD', 'ECL',
                'EL', 'SHW', 'GM', 'ADP', 'TFC', 'KLAC', 'CME', 'ATVI', 'ADSK', 'NXPI', 'BIIB',
                'SPGI', 'ICE', 'MCO', 'COF', 'TRV', 'ALL', 'MET', 'PRU', 'AIG', 'AFL', 'HIG',

                # NASDAQ 100 + Growth Stocks (All US-traded)
                'INTC', 'CMCSA', 'PYPL', 'CHTR', 'MRVL', 'MRNA', 'ABNB', 'MELI', 'DXCM', 'ILMN',
                'BABA', 'JD', 'BIDU', 'NIO', 'XPEV', 'LI', 'TEAM', 'CRWD', 'DOCU', 'PTON', 'ZM',
                'ROKU', 'SNAP', 'UBER', 'LYFT', 'PINS', 'SPOT', 'SQ', 'SHOP', 'SNOW', 'PLTR',
                'RBLX', 'COIN', 'HOOD', 'SOFI', 'UPST', 'AFRM', 'DKNG', 'PENN', 'FUBO', 'SKLZ',

                # Financial Sector (ALL major banks, insurance, fintech)
                'WFC', 'MS', 'SCHW', 'BLK', 'SPGI', 'ICE', 'MCO', 'COF', 'TRV', 'ALL', 'MET',
                'PRU', 'AIG', 'AFL', 'HIG', 'PNC', 'USB', 'TFC', 'MTB', 'FITB', 'HBAN', 'RF',
                'CFG', 'KEY', 'ZION', 'CMA', 'PBCT', 'WAL', 'SIVB', 'ALLY', 'LC', 'TREE',

                # Healthcare & Biotech (MASSIVE list)
                'ABBV', 'JNJ', 'PFE', 'MRK', 'TMO', 'ABT', 'DHR', 'AMGN', 'GILD', 'VRTX', 'REGN',
                'BIIB', 'ILMN', 'MRNA', 'BNTX', 'ZTS', 'CVS', 'CI', 'UNH', 'ANTM', 'HUM', 'CNC',
                'MOH', 'WCG', 'EW', 'HOLX', 'DXCM', 'ALGN', 'IDXX', 'IQV', 'A', 'WAT', 'MTD',
                'NVAX', 'SGEN', 'ALNY', 'BMRN', 'TECH', 'SRPT', 'RARE', 'BLUE', 'FOLD', 'ARWR',
                'EDIT', 'CRSP', 'NTLA', 'BEAM', 'PRIME', 'VERV', 'SGMO', 'FATE', 'RGNX', 'IONS',
                'BIOL', 'BNGO', 'OCGN', 'SENS', 'JAGX', 'EXPR', 'CLOV', 'SAVA', 'AXSM', 'PTCT',

                # Energy Sector (ALL oil, gas, renewable)
                'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR', 'HAL',
                'DVN', 'FANG', 'EQT', 'CTRA', 'MRO', 'APA', 'HES', 'KMI', 'OKE', 'WMB', 'EPD',
                'ET', 'MPLX', 'PAA', 'PAGP', 'TRGP', 'ONEOK', 'ENPH', 'SEDG', 'FSLR', 'SPWR',
                'RUN', 'NOVA', 'PLUG', 'FCEL', 'BLDP', 'GEVO', 'BE', 'CLNE', 'HYLN', 'NKLA',

                # Consumer Discretionary (ALL retail, entertainment, automotive)
                'AMZN', 'TSLA', 'HD', 'NKE', 'SBUX', 'TGT', 'LOW', 'TJX', 'BKNG', 'MCD', 'DIS',
                'NFLX', 'CMCSA', 'F', 'GM', 'RIVN', 'LCID', 'GOEV', 'HYLN', 'FSR', 'CCIV',
                'EBAY', 'ETSY', 'W', 'WAYFAIR', 'CHWY', 'PETS', 'WOOF', 'BARK', 'PETQ',

                # US Penny Stock Collection (Under $5) - US companies only
                'SNDL', 'NAKD', 'GNUS', 'XSPA', 'SHIP', 'TOPS', 'GLBS', 'CTRM', 'SOLO', 'IDEX',
                'AMC', 'GME', 'BB', 'KOSS', 'WKHS', 'RIDE', 'SPCE', 'TLRY', 'CGC', 'ACB',
                'CRON', 'HEXO', 'OGI', 'GRWG', 'IIPR', 'KERN', 'XXII', 'HEMP', 'CBDD', 'CBDL',
                'MJNA', 'TRTC', 'ERBB', 'DEWM', 'MCOA', 'USMJ', 'CBIS', 'GRNH', 'MDBX', 'RMHB',
                'DIGP', 'MINE', 'SEEK', 'SIRC', 'OZSC', 'AITX', 'ABML', 'ALPP', 'PASO', 'TLSS',
                'HPNN', 'PHIL', 'INND', 'SFOR', 'BNGO', 'OCGN', 'SENS', 'JAGX', 'EXPR', 'CLOV',

                # US Crypto & Blockchain Companies
                'MSTR', 'RIOT', 'MARA', 'COIN', 'HOOD', 'SQ', 'PYPL',

                # Russell 2000 Small Cap (Comprehensive Sample)
                'AACG', 'AADI', 'AAIC', 'AAME', 'AAON', 'AAWW', 'ABCB', 'ABCL', 'ABCM', 'ABEO',
                'ABIO', 'ABLV', 'ABMD', 'ABOS', 'ABSI', 'ABST', 'ABUS', 'ACAD', 'ACCD', 'ACCO',
                'ACEL', 'ACER', 'ACES', 'ACET', 'ACGL', 'ACHC', 'ACHL', 'ACHR', 'ACHV', 'ACIA',
                'ACLS', 'ACMR', 'ACNB', 'ACRS', 'ACRX', 'ACST', 'ACTG', 'ADCT', 'ADES', 'ADIL',
                'ADMA', 'ADMP', 'ADMS', 'ADNT', 'ADOC', 'ADPT', 'ADRO', 'ADTN', 'ADUS', 'ADVM',

                # Small Cap Growth & Emerging
                'ROKU', 'PTON', 'ZM', 'DOCU', 'SNOW', 'PLTR', 'RBLX', 'UPST', 'AFRM', 'DKNG',
                'PENN', 'FUBO', 'SKLZ', 'CLOV', 'SOFI', 'HOOD', 'COIN', 'OPEN', 'WISH', 'SPCE',

                # REITs (ALL major real estate)
                'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
                'AVB', 'EQR', 'VTR', 'ARE', 'MAA', 'ESS', 'UDR', 'CPT', 'FRT', 'REG',
                'SPG', 'VICI', 'WY', 'AMH', 'INVH', 'ELS', 'SUI', 'CUBE', 'LSI', 'REXR',

                # Utilities (ALL power, water, gas)
                'NEE', 'DUK', 'SO', 'D', 'EXC', 'XEL', 'SRE', 'AEP', 'PCG', 'ED', 'EIX',
                'ETR', 'ES', 'FE', 'AEE', 'CMS', 'DTE', 'NI', 'LNT', 'EVRG', 'PNW', 'IDA',

                # Materials & Mining (ALL commodities)
                'LIN', 'APD', 'ECL', 'SHW', 'FCX', 'NEM', 'GOLD', 'AA', 'X', 'CLF', 'NUE',
                'STLD', 'RS', 'VMC', 'MLM', 'EMN', 'DD', 'DOW', 'LYB', 'CF', 'MOS', 'FMC',

                # Industrial (ALL manufacturing, aerospace, defense)
                'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'FDX', 'LMT', 'RTX', 'NOC', 'DE',
                'EMR', 'ETN', 'ITW', 'PH', 'ROK', 'DOV', 'XYL', 'CARR', 'OTIS', 'IR', 'FAST',

                # Communication Services
                'GOOGL', 'META', 'NFLX', 'DIS', 'CMCSA', 'VZ', 'T', 'CHTR', 'TMUS', 'DISH',
                'TWTR', 'SNAP', 'PINS', 'MTCH', 'IAC', 'BMBL', 'YELP', 'GRPN', 'QNST', 'CARS'
        ]

        # Remove duplicates and sort
        self.all_tickers = sorted(list(set(self.all_tickers)))
        self.nasdaq_tickers = [t for t in self.all_tickers if t in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA']]
        self.nyse_tickers = [t for t in self.all_tickers if t in ['JPM', 'JNJ', 'V', 'UNH', 'HD', 'PG', 'MA', 'BAC']]
        self.amex_tickers = [t for t in self.all_tickers if t not in self.nasdaq_tickers and t not in self.nyse_tickers]

        logger.warning(f"⚠️ Using comprehensive fallback ticker list: {len(self.all_tickers)} tickers")
        logger.info(f"📊 Fallback distribution - NASDAQ: {len(self.nasdaq_tickers)}, NYSE: {len(self.nyse_tickers)}, Other: {len(self.amex_tickers)}")

    def is_market_open(self) -> bool:
        """Determine if US stock market is currently open"""
        try:
            # Get current time in Eastern timezone
            now_et = datetime.now(self.market_tz)

            # Check if it's a weekday (Monday=0, Sunday=6)
            if now_et.weekday() >= 5:  # Saturday or Sunday
                logger.debug("Market closed: Weekend")
                return False

            # Check if it's during market hours (9:30 AM - 4:00 PM ET)
            market_open = now_et.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = now_et.replace(hour=16, minute=0, second=0, microsecond=0)

            is_open = market_open <= now_et <= market_close

            if not is_open:
                logger.debug(f"Market closed: Current time {now_et.strftime('%H:%M:%S ET')}, Market hours: 09:30-16:00 ET")
            else:
                logger.debug(f"Market open: Current time {now_et.strftime('%H:%M:%S ET')}")

            return is_open

        except Exception as e:
            logger.error(f"Error checking market hours: {e}")
            return False

    def should_scan_now(self) -> bool:
        """Legacy method - kept for compatibility"""
        return self.is_market_open()
    
    def get_scan_list(self, scan_type: str = 'comprehensive', batch_size: int = None) -> List[str]:
        """Get list of stocks to scan based on type"""
        try:
            if scan_type == 'all_companies':
                # Return ALL public companies
                tickers = self.all_tickers.copy()
            elif scan_type == 'nasdaq':
                tickers = self.nasdaq_tickers.copy()
            elif scan_type == 'nyse':
                tickers = self.nyse_tickers.copy()
            elif scan_type == 'amex':
                tickers = self.amex_tickers.copy()
            elif scan_type == 'penny_stocks':
                # Filter for penny stocks (typically under $5)
                tickers = [t for t in self.all_tickers if any(penny in t for penny in ['SNDL', 'NAKD', 'GNUS', 'XSPA', 'SHIP', 'TOPS', 'GLBS', 'CTRM', 'SOLO', 'IDEX'])]
            elif scan_type == 'comprehensive':
                # Use all tickers for comprehensive scanning
                tickers = self.all_tickers.copy()
            else:
                # Default to comprehensive
                tickers = self.all_tickers.copy()

            # Apply batch size limit if specified
            if batch_size and len(tickers) > batch_size:
                # Rotate through different batches to eventually cover all stocks
                import random
                random.shuffle(tickers)
                tickers = tickers[:batch_size]
                logger.info(f"📊 Limited to {batch_size} tickers from {len(self.all_tickers)} total (rotating selection)")

            return tickers

        except Exception as e:
            logger.error(f"Error getting scan list: {e}")
            return []

    def scan_market_multi_timeframe(self, symbols: List[str]) -> Dict:
        """Scan market with multiple timeframes as requested"""
        try:
            logger.info(f"🔍 Multi-timeframe scanning {len(symbols)} symbols...")
            logger.info(f"📊 Timeframes: {self.prediction_timeframes} days")

            total_predictions = 0
            successful_predictions = 0
            errors = []

            for symbol in symbols:
                try:
                    logger.info(f"📈 Processing {symbol}...")

                    # Make predictions for all timeframes: 1, 5, 10, 30 days
                    for timeframe in self.prediction_timeframes:
                        try:
                            result = self.prediction_service.make_prediction(
                                symbol,
                                timeframe_days=timeframe
                            )

                            total_predictions += 1

                            if result:
                                successful_predictions += 1
                                logger.info(f"✅ {symbol} ({timeframe}d): {result.get('prediction', 'N/A')} ({result.get('confidence', 0)}%)")
                            else:
                                logger.warning(f"⚠️ Failed prediction for {symbol} ({timeframe}d)")

                            # Small delay between timeframe predictions
                            time.sleep(1)

                        except Exception as e:
                            logger.error(f"❌ Error predicting {symbol} ({timeframe}d): {e}")
                            errors.append(f"{symbol}_{timeframe}d: {str(e)}")

                    # Delay between stocks to avoid overwhelming the system
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"❌ Error processing {symbol}: {e}")
                    errors.append(f"{symbol}: {str(e)}")

            return {
                'status': 'completed',
                'total_predictions': total_predictions,
                'successful_predictions': successful_predictions,
                'success_rate': successful_predictions / total_predictions if total_predictions > 0 else 0,
                'symbols_processed': len(symbols),
                'timeframes': self.prediction_timeframes,
                'errors': errors,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Error in multi-timeframe market scan: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def scan_market(self, symbols: List[str], timeframe_days: int = 7) -> Dict:
        """Scan specific market symbols (legacy single timeframe method)"""
        try:
            logger.info(f"Scanning {len(symbols)} symbols with {timeframe_days} day timeframe...")

            processed_count = 0
            errors = []

            for symbol in symbols:
                try:
                    # Make prediction for each symbol
                    result = self.prediction_service.make_prediction(
                        symbol,
                        timeframe_days=timeframe_days
                    )

                    if result and result.get('status') == 'success':
                        processed_count += 1
                        logger.info(f"✅ Processed {symbol}: {result.get('prediction', 'N/A')}")
                    else:
                        error_msg = f"Failed to process {symbol}"
                        errors.append(error_msg)
                        logger.warning(error_msg)

                except Exception as e:
                    error_msg = f"Error processing {symbol}: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                    continue

            logger.info(f"Market scan completed: {processed_count}/{len(symbols)} processed")

            return {
                'status': 'success',
                'processed_count': processed_count,
                'total_symbols': len(symbols),
                'errors': errors
            }

        except Exception as e:
            logger.error(f"Error in market scan: {e}")
            return {'status': 'error', 'error': str(e)}

    def scan_and_predict_autonomous(self, scan_type: str = 'comprehensive', batch_size: int = None) -> Dict:
        """Perform autonomous multi-timeframe scan and prediction cycle"""
        try:
            logger.info(f"🤖 Starting autonomous {scan_type} scan cycle")
            logger.info(f"📊 Multi-timeframe predictions: {self.prediction_timeframes} days")

            # Check market status for HFT mode
            if scan_type == 'all_companies' and not self.is_market_open():
                logger.info("🛑 Market closed - skipping HFT scan")
                return {
                    'status': 'market_closed',
                    'reason': 'market_hours_ended',
                    'timestamp': datetime.now().isoformat()
                }

            # Get stocks to scan with batch size
            symbols = self.get_scan_list(scan_type, batch_size=batch_size)
            if not symbols:
                logger.warning("No symbols to scan")
                return {
                    'status': 'error',
                    'reason': 'no_symbols',
                    'timestamp': datetime.now().isoformat()
                }

            logger.info(f"🔍 Scanning {len(symbols)} symbols: {symbols[:5]}{'...' if len(symbols) > 5 else ''}")
            if scan_type == 'all_companies':
                logger.info(f"🌍 Comprehensive market scan: {len(symbols)} of {len(self.all_tickers)} total companies")

            # Perform multi-timeframe scanning
            results = self.scan_market_multi_timeframe(symbols)

            # Add scan metadata
            results.update({
                'scan_type': scan_type,
                'symbols_scanned': symbols,
                'autonomous_mode': True,
                'batch_size': batch_size,
                'total_available': len(self.all_tickers)
            })

            logger.info(f"🎯 Autonomous scan completed: {results.get('successful_predictions', 0)}/{results.get('total_predictions', 0)} predictions successful")

            return results

        except Exception as e:
            logger.error(f"❌ Error during autonomous scan cycle: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def scan_and_predict(self, scan_type: str = 'sp500') -> Dict:
        """Perform a complete scan and prediction cycle (legacy method)"""
        try:
            logger.info(f"Starting {scan_type} scan cycle")

            # Check if we should scan
            if not self.should_scan_now():
                logger.info("Skipping scan - conditions not met")
                return {
                    'status': 'skipped',
                    'reason': 'market_closed_or_weekend',
                    'timestamp': datetime.now().isoformat()
                }

            # Get stocks to scan
            symbols = self.get_scan_list(scan_type)
            if not symbols:
                logger.warning("No symbols to scan")
                return {
                    'status': 'error',
                    'reason': 'no_symbols',
                    'timestamp': datetime.now().isoformat()
                }

            logger.info(f"Scanning {len(symbols)} symbols: {symbols}")

            # Make predictions
            predictions = self.prediction_service.batch_predictions(symbols)

            # Compile results
            results = {
                'status': 'completed',
                'scan_type': scan_type,
                'symbols_scanned': len(symbols),
                'predictions_made': len(predictions),
                'predictions': predictions,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Scan completed: {len(predictions)}/{len(symbols)} predictions made")

            return results

        except Exception as e:
            logger.error(f"Error during scan cycle: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def continuous_scan_worker(self):
        """Worker thread for continuous autonomous multi-timeframe scanning with market hours awareness"""
        logger.info("🤖 Starting autonomous HFT scan worker")
        logger.info(f"📊 Prediction timeframes: {self.prediction_timeframes} days")
        logger.info(f"⚡ HFT scan interval: {self.scan_interval} seconds ({self.scan_interval/60:.1f} minutes)")
        logger.info(f"🌍 Scanning ALL public companies: {len(self.all_tickers)} total tickers")
        logger.info(f"🕐 Market hours: 9:30 AM - 4:00 PM ET (Mon-Fri)")

        scan_count = 0
        companies_scanned_today = set()

        while self.is_running:
            try:
                # Check if market is open
                market_open = self.is_market_open()

                if not market_open:
                    logger.info("� Market is closed - HFT training stopped")
                    logger.info("⏰ Waiting for market to open (9:30 AM ET, Mon-Fri)")

                    # Check every 5 minutes when market is closed
                    time.sleep(300)
                    continue

                scan_count += 1
                logger.info(f"⚡ HFT Scan #{scan_count} - Market is OPEN")

                # Use batch scanning to cover all companies over time
                batch_size = 50  # Scan 50 companies per cycle

                # Perform autonomous multi-timeframe scan with batch rotation
                results = self.scan_and_predict_autonomous('all_companies', batch_size=batch_size)

                # Track companies scanned today
                if 'symbols_scanned' in results:
                    companies_scanned_today.update(results['symbols_scanned'])

                if results['status'] == 'completed':
                    success_rate = results.get('success_rate', 0) * 100
                    logger.info(f"✅ HFT Scan #{scan_count} completed:")
                    logger.info(f"   📈 {results.get('successful_predictions', 0)}/{results.get('total_predictions', 0)} predictions successful ({success_rate:.1f}%)")
                    logger.info(f"   🏢 {results.get('symbols_processed', 0)} companies analyzed this cycle")
                    logger.info(f"   📊 {len(companies_scanned_today)} unique companies scanned today")
                    logger.info(f"   ⏱️  Timeframes: {results.get('timeframes', [])}")
                elif results['status'] == 'error':
                    logger.error(f"❌ HFT Scan #{scan_count} failed: {results.get('error', 'unknown error')}")
                elif results['status'] == 'market_closed':
                    logger.info("🛑 Market closed during scan - stopping HFT training")
                    break
                else:
                    logger.warning(f"⚠️ HFT Scan #{scan_count} status: {results.get('status', 'unknown')}")

                # Check if market is still open before waiting
                if self.is_running and self.is_market_open():
                    logger.info(f"⚡ Next HFT scan in {self.scan_interval} seconds")
                    time.sleep(self.scan_interval)
                else:
                    logger.info("🛑 Market closed - stopping HFT training")
                    break

            except Exception as e:
                logger.error(f"❌ Error in HFT scan worker: {e}")
                if self.is_running and self.is_market_open():
                    logger.info("⏳ Retrying in 60 seconds...")
                    time.sleep(60)  # Wait 1 minute before retrying
                else:
                    logger.info("🛑 Market closed - stopping due to error")
                    break

        logger.info(f"🏁 HFT training session ended. Scanned {len(companies_scanned_today)} unique companies today.")
    
    def start_autonomous_scanning(self):
        """Start autonomous scanning in background thread"""
        if self.is_running:
            logger.warning("Autonomous scanning already running")
            return False
        
        try:
            self.is_running = True
            self.scan_thread = threading.Thread(target=self.continuous_scan_worker, daemon=True)
            self.scan_thread.start()
            
            logger.info("Autonomous scanning started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting autonomous scanning: {e}")
            self.is_running = False
            return False
    
    def stop_autonomous_scanning(self):
        """Stop autonomous scanning"""
        if not self.is_running:
            logger.warning("Autonomous scanning not running")
            return False
        
        try:
            self.is_running = False
            
            if self.scan_thread and self.scan_thread.is_alive():
                self.scan_thread.join(timeout=5)
            
            logger.info("Autonomous scanning stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping autonomous scanning: {e}")
            return False
    
    def get_scanner_status(self) -> Dict:
        """Get current scanner status"""
        return {
            'is_running': self.is_running,
            'scan_interval': self.scan_interval,
            'market_open': self.should_scan_now(),
            'thread_alive': self.scan_thread.is_alive() if self.scan_thread else False,
            'timestamp': datetime.now().isoformat()
        }
    
    def cleanup(self):
        """Clean up scanner resources"""
        try:
            self.stop_autonomous_scanning()
            self.prediction_service.cleanup()
            logger.info("Autonomous scanner cleaned up")
        except Exception as e:
            logger.error(f"Error during scanner cleanup: {e}")
