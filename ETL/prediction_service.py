"""
StockTrek Prediction Service
Bridges neural network, preprocessing, and database components
"""

import sys
import os
import logging
import yfinance as yf
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'Neural-Network'))

from Neural_Network.neural_network_service import NeuralNetworkService
from Databases.database_service import DatabaseService
from Preprocessing.financial import fetch_data, process_data
from Preprocessing.sentiment import SentimentAnalyzer, get_company_info_from_ticker
from ETL.etl import etl_for_ticker, clean_for_json, to_jsonb
# from ETL.prediction_validator import PredictionValidator  # Removed for simplicity

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_live_stock_data(symbol: str) -> Dict:
    """Get live stock data from yfinance"""
    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info
        hist = ticker.history(period="5d")

        if hist.empty:
            return {}

        current_price = hist['Close'].iloc[-1] if not hist.empty else None
        volume = hist['Volume'].iloc[-1] if not hist.empty else None

        # Get additional live data
        live_data = {
            'current_price': float(current_price) if current_price else None,
            'volume': int(volume) if volume else None,
            'market_cap': info.get('marketCap'),
            'pe_ratio': info.get('trailingPE'),
            'eps': info.get('trailingEps'),
            'beta': info.get('beta'),
            'fifty_two_week_high': info.get('fiftyTwoWeekHigh'),
            'fifty_two_week_low': info.get('fiftyTwoWeekLow'),
            'dividend_yield': info.get('dividendYield'),
            'regular_market_price': info.get('regularMarketPrice'),
            'previous_close': info.get('previousClose'),
            'open': info.get('open'),
            'day_high': info.get('dayHigh'),
            'day_low': info.get('dayLow')
        }

        # Calculate technical indicators if we have enough data
        if len(hist) >= 20:
            live_data['moving_avg_20'] = hist['Close'].rolling(window=20).mean().iloc[-1]
        if len(hist) >= 5:
            live_data['moving_avg_5'] = hist['Close'].rolling(window=5).mean().iloc[-1]

        return live_data

    except Exception as e:
        logger.error(f"Error getting live data for {symbol}: {e}")
        return {}


class PredictionService:
    """
    Main prediction service that coordinates all components
    Handles the complete prediction pipeline from data gathering to storage
    """
    
    def __init__(self):
        """Initialize all service components"""
        self.neural_network = NeuralNetworkService()
        self.database = DatabaseService()
        self.sentiment_analyzer = SentimentAnalyzer()
        # self.validator = PredictionValidator()  # Removed for simplicity

        logger.info("Prediction service initialized")
    
    def process_company_data(self, symbol: str) -> Dict:
        """Process complete company data with bulletproof error handling"""
        logger.info(f"Processing data for {symbol}")

        # Initialize with minimal required data
        combined_data = {
            'symbol': symbol.upper(),
            'name': symbol.upper(),  # Fallback name
            'as_of_date': datetime.now().date(),
            'data_quality': 'minimal',  # Track data completeness
            'missing_fields': []
        }

        try:
            # Try to get company name
            try:
                company_name = get_company_info_from_ticker(symbol)
                if company_name and company_name.strip():
                    combined_data['name'] = company_name
                    combined_data['data_quality'] = 'basic'
                else:
                    combined_data['missing_fields'].append('company_name')
            except Exception as e:
                logger.warning(f"Could not get company name for {symbol}: {e}")
                combined_data['missing_fields'].append('company_name')

            # Get live stock data first (most important for predictions)
            try:
                logger.info(f"Getting live data for {combined_data['name']} ({symbol})")
                live_data = get_live_stock_data(symbol)

                if live_data and live_data.get('current_price'):
                    combined_data.update(live_data)
                    combined_data['price'] = live_data['current_price']  # Ensure price is set
                    combined_data['data_quality'] = 'excellent'
                    logger.info(f"Live price for {symbol}: ${live_data['current_price']:.2f}")
                else:
                    combined_data['missing_fields'].append('live_price_data')
                    logger.warning(f"No live price data available for {symbol}")
            except Exception as e:
                logger.warning(f"Error getting live data for {symbol}: {e}")
                combined_data['missing_fields'].append('live_data')

            # Try to fetch and process financial data
            try:
                logger.info(f"Processing financial data for {combined_data['name']} ({symbol})")
                financial_data = fetch_data(symbol)

                if financial_data:
                    processed_data = process_data(financial_data)
                    if processed_data:
                        # Only update fields that aren't already set by live data
                        for key, value in processed_data.items():
                            if key not in combined_data or combined_data[key] is None:
                                combined_data[key] = value
                        if combined_data['data_quality'] != 'excellent':
                            combined_data['data_quality'] = 'good'
                    else:
                        combined_data['missing_fields'].append('processed_financial_data')
                else:
                    combined_data['missing_fields'].append('raw_financial_data')

            except Exception as e:
                logger.warning(f"Could not process financial data for {symbol}: {e}")
                combined_data['missing_fields'].append('financial_data')

            # Try to get sentiment analysis
            try:
                logger.info(f"Fetching news for {combined_data['name']} ({symbol})...")
                sentiment_data = self.sentiment_analyzer.get_company_sentiment_analysis(
                    combined_data['name'], symbol
                )

                if sentiment_data and isinstance(sentiment_data, dict):
                    combined_data.update(sentiment_data)
                    if combined_data['data_quality'] == 'good':
                        combined_data['data_quality'] = 'excellent'
                else:
                    combined_data['missing_fields'].append('sentiment_data')
                    # Provide default sentiment values
                    combined_data.update({
                        'sentiment_score': 0.0,
                        'news_count': 0,
                        'overall_sentiment': 'neutral'
                    })

            except Exception as e:
                logger.warning(f"Could not get sentiment data for {symbol}: {e}")
                combined_data['missing_fields'].append('sentiment_data')
                combined_data.update({
                    'sentiment_score': 0.0,
                    'news_count': 0,
                    'overall_sentiment': 'neutral'
                })

            # Try to run ETL process
            try:
                etl_data = etl_for_ticker(symbol)
                if etl_data and isinstance(etl_data, dict):
                    # Merge carefully to avoid overwriting our bulletproof data
                    for key, value in etl_data.items():
                        if key not in combined_data or combined_data[key] is None:
                            combined_data[key] = value
                else:
                    combined_data['missing_fields'].append('etl_data')

            except Exception as e:
                logger.warning(f"ETL processing failed for {symbol}: {e}")
                combined_data['missing_fields'].append('etl_data')

            # Ensure we have minimum required fields for neural network
            self._ensure_minimum_fields(combined_data)

            logger.info(f"Data processing completed for {symbol} - Quality: {combined_data['data_quality']}")
            if combined_data['missing_fields']:
                logger.info(f"Missing fields: {combined_data['missing_fields']}")

            return combined_data

        except Exception as e:
            logger.error(f"Critical error processing {symbol}: {e}")
            # Return minimal viable data even in worst case
            return self._get_minimal_viable_data(symbol)

    def _ensure_minimum_fields(self, data: Dict):
        """Ensure minimum required fields exist with sensible defaults"""
        defaults = {
            'price': 0.0,
            'volume': 0,
            'market_cap': 0.0,
            'pe_ratio': 0.0,
            'eps': 0.0,
            'dividend_yield': 0.0,
            'beta': 1.0,  # Market average
            'fifty_two_week_high': 0.0,
            'fifty_two_week_low': 0.0,
            'moving_avg_50': 0.0,
            'moving_avg_200': 0.0,
            'rsi_14': 50.0,  # Neutral RSI
            'macd': 0.0,
            'bollinger_upper': 0.0,
            'bollinger_lower': 0.0,
            'sentiment_score': 0.0,
            'news_count': 0
        }

        for field, default_value in defaults.items():
            if field not in data or data[field] is None:
                data[field] = default_value
                if 'missing_fields' in data:
                    data['missing_fields'].append(field)

    def _get_minimal_viable_data(self, symbol: str) -> Dict:
        """Return absolute minimum data required for system to function"""
        return {
            'symbol': symbol.upper(),
            'name': symbol.upper(),
            'as_of_date': datetime.now().date(),
            'data_quality': 'emergency_fallback',
            'missing_fields': ['all_external_data'],
            'price': 1.0,  # Minimal non-zero price
            'volume': 1000,
            'market_cap': 1000000.0,
            'pe_ratio': 15.0,  # Market average
            'eps': 1.0,
            'dividend_yield': 0.0,
            'beta': 1.0,
            'fifty_two_week_high': 1.0,
            'fifty_two_week_low': 1.0,
            'moving_avg_50': 1.0,
            'moving_avg_200': 1.0,
            'rsi_14': 50.0,
            'macd': 0.0,
            'bollinger_upper': 1.0,
            'bollinger_lower': 1.0,
            'sentiment_score': 0.0,
            'news_count': 0,
            'overall_sentiment': 'neutral'
        }
    
    def store_company_data(self, symbol: str, data: Dict) -> Optional[int]:
        """Store company data in database and return company ID"""
        try:
            # Validate inputs
            if not symbol or not symbol.strip():
                logger.error("Invalid symbol provided")
                return None

            symbol = symbol.strip().upper()
            company_name = data.get('name', symbol)

            if not company_name or not company_name.strip():
                company_name = symbol

            # Get or create company record
            company = self.database.get_company_by_symbol(symbol)

            if not company:
                logger.info(f"Creating new company record for {company_name} ({symbol})")
                company_id = self.database.insert_company(
                    company_name,
                    symbol,
                    symbol
                )
            else:
                company_id = company['id']
            
            if not company_id:
                logger.error(f"Could not get/create company record for {symbol}")
                return None
            
            # Store company data
            data_id = self.database.insert_company_data(company_id, data)
            
            if data_id:
                logger.info(f"Inserted data for {data.get('name', symbol)} ({symbol})")
            
            return company_id
            
        except Exception as e:
            logger.error(f"Error storing company data: {e}")
            return None
    
    def make_prediction(self, symbol: str, timeframe_days: int = 7) -> Optional[Dict]:
        """Complete prediction pipeline for a stock symbol"""
        try:
            logger.info(f"Making prediction for {symbol} with {timeframe_days} day timeframe")
            
            # Process company data
            company_data = self.process_company_data(symbol)
            if not company_data:
                return None
            
            # Store data and get company ID
            company_id = self.store_company_data(symbol, company_data)
            if not company_id:
                return None
            
            # Make neural network prediction
            prediction = self.neural_network.predict_stock(company_data)
            if not prediction:
                logger.error(f"Neural network prediction failed for {symbol}")
                return None
            
            # Add timeframe to prediction
            prediction['timeframe_days'] = timeframe_days
            prediction['symbol'] = symbol
            prediction['company_id'] = company_id

            # Basic validation (simplified)
            prediction['anomaly_score'] = 0.0
            prediction['validation_flags'] = []

            # Store prediction in database
            try:
                success = self.database.store_prediction(company_id, prediction)
                if not success:
                    logger.error("Error storing prediction in database")
            except Exception as e:
                logger.error(f"Error storing prediction: {e}")

            # Enhanced logging with validation info
            anomaly_info = f" (🚨 Anomaly: {validation_result.get('anomaly_score', 0):.2f})" if validation_result.get('anomaly_score', 0) > 0.5 else ""
            logger.info(f"Prediction completed for {symbol}: {prediction['prediction']} ({prediction['confidence']:.1f}%){anomaly_info}")

            return prediction
            
        except Exception as e:
            logger.error(f"Error in prediction pipeline for {symbol}: {e}")
            return None
    
    def batch_predictions(self, symbols: List[str], timeframe_days: int = 7) -> List[Dict]:
        """Make predictions for multiple symbols"""
        results = []
        
        for symbol in symbols:
            try:
                prediction = self.make_prediction(symbol, timeframe_days)
                if prediction:
                    results.append(prediction)
                else:
                    logger.warning(f"Failed to get prediction for {symbol}")
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
        
        return results
    
    def get_recent_predictions(self, days_back: int = 7) -> List[Dict]:
        """Get recent predictions from database"""
        return self.database.get_recent_predictions(days_back)
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        try:
            # Neural network status
            nn_info = self.neural_network.get_model_info()
            
            # Database health metrics
            db_metrics = self.database.get_system_health_metrics()
            
            # System status
            status = {
                'neural_network': {
                    'status': 'operational' if nn_info['model_loaded'] else 'degraded',
                    'model_loaded': nn_info['model_loaded'],
                    'feature_count': nn_info['feature_count'],
                    'model_version': nn_info['model_version']
                },
                'database': {
                    'status': 'operational' if self.database.get_connection() else 'error',
                    'total_predictions': db_metrics.get('total_predictions', 0),
                    'recent_predictions': db_metrics.get('recent_predictions', 0),
                    'average_accuracy': db_metrics.get('average_accuracy', 0),
                    'total_companies': db_metrics.get('total_companies', 0)
                },
                'preprocessing': {
                    'status': 'operational',
                    'sentiment_analyzer': 'loaded'
                },
                'timestamp': datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def make_hft_prediction(self, ticker: str, timeframe_minutes: int = 5, use_cached_sentiment: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make high-frequency trading prediction with ultra-short timeframes
        """
        try:
            logger.info(f"⚡ Making HFT prediction for {ticker} ({timeframe_minutes}min)")

            # Get live price data only (skip heavy processing for speed)
            live_data = self._get_live_price_data(ticker)
            if not live_data:
                logger.error(f"❌ No live data for {ticker}")
                return None

            # Use cached sentiment if provided, otherwise skip sentiment for speed
            if use_cached_sentiment:
                live_data.update(use_cached_sentiment)
            else:
                live_data['sentiment_score'] = 0.0  # Neutral sentiment
                live_data['news_count'] = 0

            # Add minimal technical indicators for speed
            live_data.update(self._get_minimal_technical_indicators(ticker))

            # Make ultra-fast prediction
            prediction = self.neural_network.predict_stock_with_etl(
                symbol=ticker,
                timeframe_days=max(1, int(timeframe_minutes / (24 * 60)))  # Convert minutes to days, minimum 1
            )

            if prediction:
                # Add HFT-specific metadata
                prediction.update({
                    'is_hft': True,
                    'timeframe_minutes': timeframe_minutes,
                    'target_date': datetime.now() + timedelta(minutes=timeframe_minutes),
                    'data_source': 'live_minimal',
                    'sentiment_cached': bool(use_cached_sentiment)
                })

                logger.info(f"⚡ HFT prediction for {ticker}: {prediction.get('direction', 'UNKNOWN')} ({prediction.get('confidence', 0):.1f}%)")

                return prediction

            return None

        except Exception as e:
            logger.error(f"❌ HFT prediction error for {ticker}: {e}")
            return None

    def _get_live_price_data(self, ticker: str) -> Optional[Dict]:
        """
        Get minimal live price data for HFT speed
        """
        try:
            stock = yf.Ticker(ticker)
            info = stock.info
            hist = stock.history(period="1d", interval="1m")  # 1-minute data for HFT

            if hist.empty:
                return None

            current_price = float(hist['Close'].iloc[-1])
            volume = int(hist['Volume'].iloc[-1])

            return {
                'symbol': ticker,
                'price': current_price,
                'volume': volume,
                'market_cap': info.get('marketCap', 0),
                'timestamp': datetime.now()
            }

        except Exception as e:
            logger.error(f"❌ Error getting live data for {ticker}: {e}")
            return None

    def _get_minimal_technical_indicators(self, ticker: str) -> Dict:
        """
        Calculate minimal technical indicators for HFT speed
        """
        try:
            stock = yf.Ticker(ticker)
            hist = stock.history(period="5d", interval="1m")  # 5 days of minute data

            if len(hist) < 50:
                return {
                    'moving_avg_50': 0.0,
                    'rsi_14': 50.0,
                    'volatility': 0.0
                }

            # Simple moving averages
            ma_50 = hist['Close'].rolling(window=50).mean().iloc[-1]

            # Simple RSI calculation
            delta = hist['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            # Volatility
            volatility = hist['Close'].pct_change().std() * 100

            return {
                'moving_avg_50': float(ma_50) if not pd.isna(ma_50) else 0.0,
                'rsi_14': float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 50.0,
                'volatility': float(volatility) if not pd.isna(volatility) else 0.0
            }

        except Exception as e:
            logger.error(f"❌ Error calculating technical indicators for {ticker}: {e}")
            return {
                'moving_avg_50': 0.0,
                'rsi_14': 50.0,
                'volatility': 0.0
            }

    def cleanup(self):
        """Clean up resources"""
        try:
            self.database.close_connection()
            logger.info("Prediction service cleaned up")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
