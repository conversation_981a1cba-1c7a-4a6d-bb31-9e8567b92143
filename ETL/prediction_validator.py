"""
StockTrek Prediction Validation and Anomaly Detection System
Detects unrealistic predictions and flags them for review
"""

import logging
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import statistics
import yfinance as yf

from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PredictionValidator:
    """
    Comprehensive prediction validation system with anomaly detection
    """
    
    def __init__(self, ollama_host: str = "http://localhost:11434"):
        self.db_service = DatabaseService()
        self.ollama_host = ollama_host
        
        # Validation thresholds
        self.max_price_change_percent = 50.0  # Maximum realistic price change
        self.min_price_threshold = 0.01       # Minimum realistic stock price
        self.max_confidence_for_extreme = 80.0  # Max confidence for extreme predictions
        self.volatility_multiplier = 3.0     # Max change relative to historical volatility
        
        # Anomaly detection parameters
        self.anomaly_flags = []
        self.validation_history = []
        
        logger.info("🛡️ Prediction Validator initialized")
        logger.info(f"   Max price change: ±{self.max_price_change_percent}%")
        logger.info(f"   Min price threshold: ${self.min_price_threshold}")
        logger.info(f"   Ollama endpoint: {self.ollama_host}")
    
    def validate_prediction(self, prediction: Dict, ticker: str) -> Dict:
        """
        Comprehensive prediction validation with anomaly detection
        """
        try:
            logger.info(f"🔍 Validating prediction for {ticker}")
            
            validation_result = {
                'is_valid': True,
                'anomaly_score': 0.0,
                'flags': [],
                'corrections': [],
                'confidence_adjustment': 1.0,
                'should_review': False,
                'validation_details': {}
            }
            
            # Extract prediction data
            current_price = prediction.get('current_price', 0)
            predicted_price = prediction.get('predicted_price', 0)
            price_change_percent = prediction.get('price_change_percent', 0)
            confidence = prediction.get('confidence', 0)
            
            # Run validation checks
            validation_result = self._check_price_realism(
                validation_result, current_price, predicted_price, price_change_percent
            )
            
            validation_result = self._check_confidence_consistency(
                validation_result, confidence, price_change_percent
            )
            
            validation_result = self._check_historical_volatility(
                validation_result, ticker, price_change_percent
            )
            
            validation_result = self._check_market_context(
                validation_result, ticker, predicted_price, current_price
            )
            
            # Calculate overall anomaly score
            validation_result['anomaly_score'] = self._calculate_anomaly_score(validation_result)
            
            # Determine if AI review is needed
            if validation_result['anomaly_score'] > 0.7 or len(validation_result['flags']) >= 3:
                validation_result['should_review'] = True
                logger.warning(f"🚨 High anomaly score for {ticker}: {validation_result['anomaly_score']:.2f}")
            
            # Store validation result
            self._store_validation_result(ticker, prediction, validation_result)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"❌ Error validating prediction for {ticker}: {e}")
            return {
                'is_valid': False,
                'anomaly_score': 1.0,
                'flags': ['validation_error'],
                'error': str(e)
            }
    
    def _check_price_realism(self, result: Dict, current_price: float, 
                           predicted_price: float, price_change_percent: float) -> Dict:
        """Check if predicted prices are realistic"""
        
        # Check for extremely low predicted prices
        if predicted_price < self.min_price_threshold:
            result['flags'].append('unrealistic_low_price')
            result['corrections'].append(f"Predicted price ${predicted_price:.2f} is unrealistically low")
            result['confidence_adjustment'] *= 0.1  # Severely reduce confidence
            logger.warning(f"🚨 Unrealistic low price: ${predicted_price:.2f}")
        
        # Check for extreme percentage changes
        if abs(price_change_percent) > self.max_price_change_percent:
            result['flags'].append('extreme_price_change')
            result['corrections'].append(f"Price change {price_change_percent:.1f}% exceeds realistic threshold")
            result['confidence_adjustment'] *= 0.3
            logger.warning(f"🚨 Extreme price change: {price_change_percent:.1f}%")
        
        # Check for impossible price ratios
        if predicted_price > 0 and current_price > 0:
            ratio = predicted_price / current_price
            if ratio < 0.1 or ratio > 10.0:  # More than 90% drop or 900% gain
                result['flags'].append('impossible_price_ratio')
                result['corrections'].append(f"Price ratio {ratio:.2f} is impossible for normal timeframes")
                result['confidence_adjustment'] *= 0.05
                logger.warning(f"🚨 Impossible price ratio: {ratio:.2f}")
        
        return result
    
    def _check_confidence_consistency(self, result: Dict, confidence: float, 
                                    price_change_percent: float) -> Dict:
        """Check if confidence is consistent with prediction magnitude"""
        
        # High confidence with extreme predictions is suspicious
        if confidence > self.max_confidence_for_extreme and abs(price_change_percent) > 20:
            result['flags'].append('overconfident_extreme_prediction')
            result['corrections'].append(f"Confidence {confidence:.1f}% too high for {price_change_percent:.1f}% change")
            result['confidence_adjustment'] *= 0.6
            logger.warning(f"🚨 Overconfident extreme prediction: {confidence:.1f}% confidence for {price_change_percent:.1f}% change")
        
        # Very low confidence with small predictions might indicate model uncertainty
        if confidence < 30 and abs(price_change_percent) < 2:
            result['flags'].append('low_confidence_small_change')
            result['corrections'].append("Low confidence for small change suggests model uncertainty")
        
        return result
    
    def _check_historical_volatility(self, result: Dict, ticker: str, 
                                   price_change_percent: float) -> Dict:
        """Check prediction against historical volatility"""
        try:
            # Get historical data
            stock = yf.Ticker(ticker)
            hist = stock.history(period="1y")  # 1 year of data
            
            if not hist.empty:
                # Calculate historical volatility
                daily_returns = hist['Close'].pct_change().dropna()
                volatility = daily_returns.std() * 100  # Convert to percentage
                
                # Check if prediction exceeds reasonable volatility bounds
                max_reasonable_change = volatility * self.volatility_multiplier
                
                if abs(price_change_percent) > max_reasonable_change:
                    result['flags'].append('exceeds_historical_volatility')
                    result['corrections'].append(
                        f"Predicted change {price_change_percent:.1f}% exceeds "
                        f"historical volatility bounds (±{max_reasonable_change:.1f}%)"
                    )
                    result['confidence_adjustment'] *= 0.7
                    logger.warning(f"🚨 Exceeds volatility for {ticker}: {price_change_percent:.1f}% vs ±{max_reasonable_change:.1f}%")
                
                result['validation_details']['historical_volatility'] = volatility
                result['validation_details']['max_reasonable_change'] = max_reasonable_change
        
        except Exception as e:
            logger.warning(f"⚠️ Could not check historical volatility for {ticker}: {e}")
            result['flags'].append('volatility_check_failed')
        
        return result
    
    def _check_market_context(self, result: Dict, ticker: str, 
                            predicted_price: float, current_price: float) -> Dict:
        """Check prediction against market context"""
        try:
            # Get basic company info
            stock = yf.Ticker(ticker)
            info = stock.info
            
            market_cap = info.get('marketCap', 0)
            
            # Check if predicted price would create unrealistic market cap
            if market_cap > 0 and predicted_price > 0 and current_price > 0:
                shares_outstanding = market_cap / current_price
                predicted_market_cap = predicted_price * shares_outstanding
                
                # Flag if predicted market cap becomes unrealistic
                if predicted_market_cap > 10e12:  # $10 trillion
                    result['flags'].append('unrealistic_market_cap')
                    result['corrections'].append(
                        f"Predicted market cap ${predicted_market_cap/1e12:.1f}T is unrealistic"
                    )
                    result['confidence_adjustment'] *= 0.2
                
                result['validation_details']['predicted_market_cap'] = predicted_market_cap
        
        except Exception as e:
            logger.warning(f"⚠️ Could not check market context for {ticker}: {e}")
        
        return result
    
    def _calculate_anomaly_score(self, result: Dict) -> float:
        """Calculate overall anomaly score (0.0 = normal, 1.0 = highly anomalous)"""
        
        base_score = 0.0
        
        # Weight different types of anomalies
        flag_weights = {
            'unrealistic_low_price': 0.9,
            'extreme_price_change': 0.8,
            'impossible_price_ratio': 0.95,
            'overconfident_extreme_prediction': 0.6,
            'exceeds_historical_volatility': 0.7,
            'unrealistic_market_cap': 0.8,
            'low_confidence_small_change': 0.2,
            'volatility_check_failed': 0.1,
            'validation_error': 1.0
        }
        
        # Calculate weighted score
        for flag in result['flags']:
            weight = flag_weights.get(flag, 0.5)
            base_score += weight
        
        # Apply confidence adjustment impact
        confidence_impact = 1.0 - result['confidence_adjustment']
        base_score += confidence_impact * 0.5
        
        # Normalize to 0-1 range
        return min(1.0, base_score)
    
    def _store_validation_result(self, ticker: str, prediction: Dict, validation: Dict):
        """Store validation result in database"""
        try:
            # Add validation metadata to prediction
            prediction['validation_result'] = validation
            prediction['anomaly_score'] = validation['anomaly_score']
            prediction['validation_flags'] = validation['flags']
            prediction['needs_review'] = validation['should_review']
            
            # Store in validation history
            self.validation_history.append({
                'ticker': ticker,
                'timestamp': datetime.now(),
                'anomaly_score': validation['anomaly_score'],
                'flags': validation['flags']
            })
            
            logger.info(f"✅ Validation result stored for {ticker}")
            
        except Exception as e:
            logger.error(f"❌ Error storing validation result: {e}")

    async def ai_review_prediction(self, ticker: str, prediction: Dict, validation: Dict) -> Dict:
        """
        Use Ollama Gemma to review and suggest corrections for anomalous predictions
        """
        try:
            logger.info(f"🤖 AI reviewing prediction for {ticker}")

            # Prepare context for AI review
            review_context = self._prepare_ai_context(ticker, prediction, validation)

            # Generate AI review using Ollama Gemma
            ai_response = await self._query_ollama_gemma(review_context)

            if ai_response:
                # Parse AI response
                review_result = self._parse_ai_response(ai_response)

                # Store AI review
                self._store_ai_review(ticker, prediction, review_result)

                logger.info(f"🤖 AI review completed for {ticker}")
                return review_result
            else:
                logger.error(f"❌ AI review failed for {ticker}")
                return {'status': 'failed', 'error': 'No AI response'}

        except Exception as e:
            logger.error(f"❌ Error in AI review for {ticker}: {e}")
            return {'status': 'error', 'error': str(e)}

    def _prepare_ai_context(self, ticker: str, prediction: Dict, validation: Dict) -> str:
        """Prepare context for AI review"""

        context = f"""
STOCK PREDICTION ANOMALY REVIEW

Ticker: {ticker}
Current Price: ${prediction.get('current_price', 0):.2f}
Predicted Price: ${prediction.get('predicted_price', 0):.2f}
Price Change: {prediction.get('price_change_percent', 0):.2f}%
Confidence: {prediction.get('confidence', 0):.1f}%
Timeframe: {prediction.get('timeframe_days', 0)} days

ANOMALY DETECTION RESULTS:
Anomaly Score: {validation.get('anomaly_score', 0):.2f}/1.0
Flags: {', '.join(validation.get('flags', []))}
Confidence Adjustment: {validation.get('confidence_adjustment', 1.0):.2f}

VALIDATION ISSUES:
{chr(10).join(validation.get('corrections', []))}

TASK: Analyze this stock prediction and provide:
1. Is this prediction realistic? (YES/NO)
2. What specific issues do you identify?
3. What would be a more reasonable prediction range?
4. Suggested confidence level (0-100%)
5. Key factors that should be considered

Please provide a structured analysis focusing on financial realism and market behavior.
"""
        return context

    async def _query_ollama_gemma(self, context: str) -> Optional[str]:
        """Query Ollama Gemma model for prediction review"""
        try:
            payload = {
                "model": "gemma",
                "prompt": context,
                "stream": False,
                "options": {
                    "temperature": 0.3,  # Lower temperature for more focused analysis
                    "top_p": 0.9,
                    "max_tokens": 1000
                }
            }

            response = requests.post(
                f"{self.ollama_host}/api/generate",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                logger.error(f"❌ Ollama API error: {response.status_code}")
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Ollama connection error: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Ollama query error: {e}")
            return None

    def _parse_ai_response(self, ai_response: str) -> Dict:
        """Parse AI response into structured format"""
        try:
            # Simple parsing - in production, you might want more sophisticated NLP
            lines = ai_response.strip().split('\n')

            result = {
                'is_realistic': 'unknown',
                'issues_identified': [],
                'suggested_range': 'unknown',
                'suggested_confidence': 50.0,
                'key_factors': [],
                'full_response': ai_response,
                'timestamp': datetime.now().isoformat()
            }

            # Extract key information (basic parsing)
            for line in lines:
                line = line.strip().lower()

                if 'realistic' in line and ('yes' in line or 'no' in line):
                    result['is_realistic'] = 'yes' if 'yes' in line else 'no'

                if 'confidence' in line and '%' in line:
                    # Try to extract confidence percentage
                    import re
                    match = re.search(r'(\d+)%', line)
                    if match:
                        result['suggested_confidence'] = float(match.group(1))

            return result

        except Exception as e:
            logger.error(f"❌ Error parsing AI response: {e}")
            return {
                'status': 'parse_error',
                'full_response': ai_response,
                'error': str(e)
            }

    def _store_ai_review(self, ticker: str, prediction: Dict, review: Dict):
        """Store AI review result"""
        try:
            # Add AI review to prediction metadata
            if 'ai_reviews' not in prediction:
                prediction['ai_reviews'] = []

            prediction['ai_reviews'].append(review)

            # Log key findings
            if review.get('is_realistic') == 'no':
                logger.warning(f"🤖 AI flagged {ticker} prediction as unrealistic")

            if review.get('suggested_confidence', 0) < prediction.get('confidence', 0):
                logger.info(f"🤖 AI suggests lower confidence for {ticker}: {review.get('suggested_confidence', 0):.1f}%")

        except Exception as e:
            logger.error(f"❌ Error storing AI review: {e}")

    def get_validation_summary(self) -> Dict:
        """Get summary of validation results"""
        try:
            if not self.validation_history:
                return {'status': 'no_data'}

            total_validations = len(self.validation_history)
            high_anomaly_count = sum(1 for v in self.validation_history if v['anomaly_score'] > 0.7)

            # Most common flags
            all_flags = []
            for v in self.validation_history:
                all_flags.extend(v['flags'])

            flag_counts = {}
            for flag in all_flags:
                flag_counts[flag] = flag_counts.get(flag, 0) + 1

            return {
                'total_validations': total_validations,
                'high_anomaly_count': high_anomaly_count,
                'anomaly_rate': high_anomaly_count / total_validations if total_validations > 0 else 0,
                'common_flags': sorted(flag_counts.items(), key=lambda x: x[1], reverse=True)[:5],
                'average_anomaly_score': statistics.mean([v['anomaly_score'] for v in self.validation_history])
            }

        except Exception as e:
            logger.error(f"❌ Error generating validation summary: {e}")
            return {'status': 'error', 'error': str(e)}
