#!/usr/bin/env python3
"""
StockTrek Prediction Review Dashboard
Provides tools to review, analyze, and learn from prediction anomalies
"""

import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import statistics

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from Databases.database_service import DatabaseService
from ETL.prediction_validator import PredictionValidator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PredictionDashboard:
    """
    Dashboard for reviewing and analyzing prediction anomalies
    """
    
    def __init__(self):
        self.db_service = DatabaseService()
        self.validator = PredictionValidator()
        
        logger.info("🔍 Prediction Dashboard initialized")
    
    def get_anomalous_predictions(self, days_back: int = 7, min_anomaly_score: float = 0.5) -> List[Dict]:
        """Get predictions with high anomaly scores"""
        try:
            query = """
                SELECT p.*, c.ticker as symbol, c.name
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.prediction_date >= %s
                AND p.prediction_metadata->>'anomaly_score' IS NOT NULL
                AND CAST(p.prediction_metadata->>'anomaly_score' AS FLOAT) >= %s
                ORDER BY CAST(p.prediction_metadata->>'anomaly_score' AS FLOAT) DESC
            """
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            results = self.db_service.execute_query(query, (cutoff_date, min_anomaly_score), fetch=True)
            
            if results:
                logger.info(f"📊 Found {len(results)} anomalous predictions in last {days_back} days")
                return results
            else:
                logger.info(f"✅ No anomalous predictions found in last {days_back} days")
                return []
                
        except Exception as e:
            logger.error(f"❌ Error getting anomalous predictions: {e}")
            return []
    
    def analyze_prediction_patterns(self, days_back: int = 30) -> Dict:
        """Analyze patterns in prediction anomalies"""
        try:
            logger.info(f"📈 Analyzing prediction patterns for last {days_back} days")
            
            # Get all predictions with validation data
            query = """
                SELECT p.*, c.ticker as symbol, c.name
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.prediction_date >= %s
                AND p.prediction_metadata IS NOT NULL
                ORDER BY p.prediction_date DESC
            """
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            predictions = self.db_service.execute_query(query, (cutoff_date,), fetch=True)
            
            if not predictions:
                return {'status': 'no_data'}
            
            analysis = {
                'total_predictions': len(predictions),
                'anomaly_distribution': {},
                'common_flags': {},
                'ticker_anomaly_rates': {},
                'timeframe_patterns': {},
                'confidence_patterns': {},
                'worst_predictions': []
            }
            
            anomaly_scores = []
            all_flags = []
            ticker_stats = {}
            timeframe_stats = {}
            
            for pred in predictions:
                try:
                    metadata = pred.get('prediction_metadata', {})
                    if isinstance(metadata, str):
                        metadata = json.loads(metadata)
                    
                    anomaly_score = float(metadata.get('anomaly_score', 0))
                    anomaly_scores.append(anomaly_score)
                    
                    # Track flags
                    flags = metadata.get('validation_flags', [])
                    all_flags.extend(flags)
                    
                    # Track by ticker
                    ticker = pred.get('symbol', 'UNKNOWN')
                    if ticker not in ticker_stats:
                        ticker_stats[ticker] = {'count': 0, 'anomaly_sum': 0}
                    ticker_stats[ticker]['count'] += 1
                    ticker_stats[ticker]['anomaly_sum'] += anomaly_score
                    
                    # Track by timeframe
                    timeframe = pred.get('timeframe_days', 0)
                    if timeframe not in timeframe_stats:
                        timeframe_stats[timeframe] = {'count': 0, 'anomaly_sum': 0}
                    timeframe_stats[timeframe]['count'] += 1
                    timeframe_stats[timeframe]['anomaly_sum'] += anomaly_score
                    
                    # Track worst predictions
                    if anomaly_score > 0.7:
                        analysis['worst_predictions'].append({
                            'ticker': ticker,
                            'anomaly_score': anomaly_score,
                            'flags': flags,
                            'confidence': pred.get('confidence_score', 0),
                            'prediction_date': pred.get('prediction_date', '').strftime('%Y-%m-%d %H:%M') if pred.get('prediction_date') else 'Unknown'
                        })
                
                except Exception as e:
                    logger.warning(f"⚠️ Error processing prediction: {e}")
                    continue
            
            # Calculate distributions
            if anomaly_scores:
                analysis['anomaly_distribution'] = {
                    'mean': statistics.mean(anomaly_scores),
                    'median': statistics.median(anomaly_scores),
                    'max': max(anomaly_scores),
                    'high_anomaly_count': sum(1 for score in anomaly_scores if score > 0.7),
                    'high_anomaly_rate': sum(1 for score in anomaly_scores if score > 0.7) / len(anomaly_scores)
                }
            
            # Count common flags
            flag_counts = {}
            for flag in all_flags:
                flag_counts[flag] = flag_counts.get(flag, 0) + 1
            analysis['common_flags'] = sorted(flag_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # Calculate ticker anomaly rates
            for ticker, stats in ticker_stats.items():
                if stats['count'] > 0:
                    analysis['ticker_anomaly_rates'][ticker] = {
                        'count': stats['count'],
                        'avg_anomaly_score': stats['anomaly_sum'] / stats['count']
                    }
            
            # Calculate timeframe patterns
            for timeframe, stats in timeframe_stats.items():
                if stats['count'] > 0:
                    analysis['timeframe_patterns'][timeframe] = {
                        'count': stats['count'],
                        'avg_anomaly_score': stats['anomaly_sum'] / stats['count']
                    }
            
            # Sort worst predictions
            analysis['worst_predictions'] = sorted(
                analysis['worst_predictions'], 
                key=lambda x: x['anomaly_score'], 
                reverse=True
            )[:10]
            
            logger.info(f"📊 Analysis complete: {analysis['total_predictions']} predictions analyzed")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ Error analyzing prediction patterns: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def generate_improvement_recommendations(self, analysis: Dict) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        try:
            # Check anomaly rate
            anomaly_rate = analysis.get('anomaly_distribution', {}).get('high_anomaly_rate', 0)
            if anomaly_rate > 0.2:  # More than 20% high anomalies
                recommendations.append(
                    f"🚨 High anomaly rate ({anomaly_rate:.1%}). Consider retraining the neural network."
                )
            
            # Check common flags
            common_flags = analysis.get('common_flags', [])
            if common_flags:
                top_flag = common_flags[0]
                if top_flag[1] > 5:  # More than 5 occurrences
                    recommendations.append(
                        f"🔧 Most common issue: '{top_flag[0]}' ({top_flag[1]} times). "
                        f"Focus on improving this aspect of the model."
                    )
            
            # Check ticker-specific issues
            ticker_rates = analysis.get('ticker_anomaly_rates', {})
            problematic_tickers = [
                ticker for ticker, stats in ticker_rates.items() 
                if stats['avg_anomaly_score'] > 0.6 and stats['count'] >= 3
            ]
            
            if problematic_tickers:
                recommendations.append(
                    f"📈 Problematic tickers: {', '.join(problematic_tickers[:5])}. "
                    f"Consider additional data sources or sector-specific models."
                )
            
            # Check timeframe patterns
            timeframe_patterns = analysis.get('timeframe_patterns', {})
            problematic_timeframes = [
                str(tf) for tf, stats in timeframe_patterns.items()
                if stats['avg_anomaly_score'] > 0.6 and stats['count'] >= 3
            ]
            
            if problematic_timeframes:
                recommendations.append(
                    f"⏰ Problematic timeframes: {', '.join(problematic_timeframes)} days. "
                    f"Consider timeframe-specific model tuning."
                )
            
            # General recommendations
            if not recommendations:
                recommendations.append("✅ Prediction quality looks good! Continue monitoring.")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"❌ Error generating recommendations: {e}")
            return ["❌ Error generating recommendations"]
    
    def print_dashboard_report(self, days_back: int = 7):
        """Print a comprehensive dashboard report"""
        try:
            print("\n" + "="*80)
            print("🔍 STOCKTREK PREDICTION ANOMALY DASHBOARD")
            print("="*80)
            
            # Get anomalous predictions
            anomalous = self.get_anomalous_predictions(days_back)
            
            print(f"\n📊 RECENT ANOMALOUS PREDICTIONS (Last {days_back} days)")
            print("-" * 50)
            
            if anomalous:
                for i, pred in enumerate(anomalous[:10], 1):
                    try:
                        metadata = pred.get('prediction_metadata', {})
                        if isinstance(metadata, str):
                            metadata = json.loads(metadata)
                        
                        anomaly_score = metadata.get('anomaly_score', 0)
                        flags = metadata.get('validation_flags', [])
                        
                        print(f"{i:2d}. {pred.get('symbol', 'UNKNOWN'):6s} | "
                              f"Anomaly: {anomaly_score:.2f} | "
                              f"Confidence: {pred.get('confidence_score', 0):5.1f}% | "
                              f"Flags: {', '.join(flags[:2])}")
                    except Exception as e:
                        print(f"{i:2d}. Error displaying prediction: {e}")
            else:
                print("✅ No anomalous predictions found!")
            
            # Get analysis
            analysis = self.analyze_prediction_patterns(30)
            
            if analysis.get('status') != 'error':
                print(f"\n📈 PATTERN ANALYSIS (Last 30 days)")
                print("-" * 50)
                
                dist = analysis.get('anomaly_distribution', {})
                print(f"Total Predictions: {analysis.get('total_predictions', 0)}")
                print(f"High Anomaly Rate: {dist.get('high_anomaly_rate', 0):.1%}")
                print(f"Average Anomaly Score: {dist.get('mean', 0):.2f}")
                
                print(f"\n🚩 COMMON ISSUES:")
                common_flags = analysis.get('common_flags', [])[:5]
                for flag, count in common_flags:
                    print(f"   • {flag}: {count} times")
                
                print(f"\n💡 RECOMMENDATIONS:")
                recommendations = self.generate_improvement_recommendations(analysis)
                for rec in recommendations:
                    print(f"   {rec}")
            
            print("\n" + "="*80)
            
        except Exception as e:
            logger.error(f"❌ Error generating dashboard report: {e}")
            print(f"❌ Error generating dashboard report: {e}")


def main():
    """Main dashboard function"""
    try:
        dashboard = PredictionDashboard()
        dashboard.print_dashboard_report()
        
    except Exception as e:
        logger.error(f"❌ Dashboard error: {e}")


if __name__ == "__main__":
    main()
