"""
StockTrek High-Frequency Trading Mode
Ultra-fast prediction cycles with 5-minute intervals and rapid self-improvement
"""

import asyncio
import logging
import time
import json
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
import threading

from .prediction_service import PredictionService
from .backtesting_service import BacktestingService
from Databases.database_service import DatabaseService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HFTMode:
    """
    High-Frequency Trading Mode for rapid prediction and learning cycles
    """
    
    def __init__(self):
        self.prediction_service = PredictionService()
        self.backtesting_service = BacktestingService()
        self.db_service = DatabaseService()
        
        # HFT Configuration
        self.prediction_interval_minutes = 5  # 5-minute prediction cycles
        self.sentiment_refresh_hours = 24     # Refresh sentiment every 24 hours
        self.max_concurrent_predictions = 10  # Parallel processing limit
        
        # Performance tracking
        self.cycle_count = 0
        self.total_predictions = 0
        self.accuracy_history = []
        self.learning_rate = 0.01
        
        # Sentiment cache with timestamps
        self.sentiment_cache = {}
        self.sentiment_timestamps = {}
        
        # Active stocks for HFT
        self.hft_stocks = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'SPY', 'QQQ', 'IWM', 'GLD', 'SLV', 'VIX', 'TLT', 'XLF'
        ]
        
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent_predictions)
        
        logger.info("🚀 HFT Mode initialized")
        logger.info(f"⚡ Prediction interval: {self.prediction_interval_minutes} minutes")
        logger.info(f"📊 Sentiment refresh: {self.sentiment_refresh_hours} hours")
        logger.info(f"🎯 Target stocks: {len(self.hft_stocks)} symbols")
    
    async def start_hft_mode(self):
        """
        Start the high-frequency trading mode
        """
        logger.info("🚀 Starting HFT Mode - Ultra-Fast Learning Cycle")
        self.is_running = True
        
        # Start parallel tasks
        tasks = [
            asyncio.create_task(self._hft_prediction_loop()),
            asyncio.create_task(self._rapid_backtesting_loop()),
            asyncio.create_task(self._sentiment_refresh_loop()),
            asyncio.create_task(self._performance_monitoring_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except KeyboardInterrupt:
            logger.info("🛑 HFT Mode stopped by user")
            self.is_running = False
        except Exception as e:
            logger.error(f"❌ HFT Mode error: {e}")
            self.is_running = False
    
    async def _hft_prediction_loop(self):
        """
        Main HFT prediction loop - 5-minute cycles
        """
        while self.is_running:
            try:
                cycle_start = time.time()
                self.cycle_count += 1
                
                logger.info(f"⚡ HFT Cycle #{self.cycle_count} - {datetime.now().strftime('%H:%M:%S')}")
                
                # Process stocks in parallel batches
                batch_size = self.max_concurrent_predictions
                for i in range(0, len(self.hft_stocks), batch_size):
                    batch = self.hft_stocks[i:i + batch_size]
                    
                    # Submit batch for parallel processing
                    futures = []
                    for ticker in batch:
                        future = self.executor.submit(self._make_hft_prediction, ticker)
                        futures.append(future)
                    
                    # Wait for batch completion
                    for future in futures:
                        try:
                            result = future.result(timeout=60)  # 1-minute timeout per prediction
                            if result:
                                self.total_predictions += 1
                        except Exception as e:
                            logger.error(f"❌ HFT prediction error: {e}")
                
                cycle_duration = time.time() - cycle_start
                logger.info(f"✅ HFT Cycle #{self.cycle_count} completed in {cycle_duration:.2f}s")
                
                # Wait for next cycle (5 minutes total)
                sleep_time = max(0, (self.prediction_interval_minutes * 60) - cycle_duration)
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except Exception as e:
                logger.error(f"❌ HFT prediction loop error: {e}")
                await asyncio.sleep(30)  # Brief pause before retry
    
    def _make_hft_prediction(self, ticker: str) -> bool:
        """
        Make a single HFT prediction with 5-minute timeframe
        """
        try:
            # Check if we need fresh sentiment data
            needs_sentiment = self._needs_sentiment_refresh(ticker)
            
            # Use cached sentiment if available and fresh
            sentiment_data = self.sentiment_cache.get(ticker) if not needs_sentiment else None
            
            # Make ultra-short prediction (5 minutes)
            prediction = self.prediction_service.make_hft_prediction(
                ticker=ticker,
                timeframe_minutes=5,
                use_cached_sentiment=sentiment_data
            )
            
            if prediction:
                # Store with HFT flag
                prediction['is_hft'] = True
                prediction['cycle_number'] = self.cycle_count
                
                # Enhanced confidence based on recent performance
                if self.accuracy_history:
                    recent_accuracy = sum(self.accuracy_history[-10:]) / min(10, len(self.accuracy_history))
                    prediction['performance_adjusted_confidence'] = prediction['confidence'] * (recent_accuracy / 100)
                
                logger.info(f"⚡ HFT {ticker}: {prediction['direction']} ({prediction['confidence']:.1f}%)")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ HFT prediction error for {ticker}: {e}")
            return False
    
    def _needs_sentiment_refresh(self, ticker: str) -> bool:
        """
        Check if sentiment data needs refreshing (24-hour cycle)
        """
        if ticker not in self.sentiment_timestamps:
            return True
        
        last_refresh = self.sentiment_timestamps[ticker]
        hours_since_refresh = (datetime.now() - last_refresh).total_seconds() / 3600
        
        return hours_since_refresh >= self.sentiment_refresh_hours
    
    async def _rapid_backtesting_loop(self):
        """
        Rapid backtesting loop - evaluate predictions every minute
        """
        while self.is_running:
            try:
                # Evaluate predictions that are 5+ minutes old
                cutoff_time = datetime.now() - timedelta(minutes=5)
                
                evaluated_count = await self._evaluate_recent_predictions(cutoff_time)
                
                if evaluated_count > 0:
                    logger.info(f"🔄 Rapid backtest: {evaluated_count} predictions evaluated")
                
                # Update learning metrics
                await self._update_learning_metrics()
                
                # Sleep for 1 minute before next evaluation
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"❌ Rapid backtesting error: {e}")
                await asyncio.sleep(30)
    
    async def _evaluate_recent_predictions(self, cutoff_time: datetime) -> int:
        """
        Evaluate recent HFT predictions and update neural network
        """
        try:
            # Get predictions ready for evaluation
            query = """
                SELECT p.*, c.ticker
                FROM neural_network_predictions p
                JOIN companies c ON p.company_id = c.id
                WHERE p.target_date <= %s 
                AND p.accuracy_score IS NULL
                AND p.is_hft = TRUE
                ORDER BY p.target_date ASC
                LIMIT 50
            """
            
            predictions = self.db_service.execute_query(query, (cutoff_time,), fetch=True)
            
            if not predictions:
                return 0
            
            evaluated_count = 0
            for pred in predictions:
                try:
                    # Get current price and calculate accuracy
                    accuracy = await self._calculate_prediction_accuracy(pred)
                    
                    if accuracy is not None:
                        # Update prediction with accuracy
                        await self._update_prediction_accuracy(pred['id'], accuracy)
                        
                        # Reward/punish neural network
                        await self._apply_learning_feedback(pred, accuracy)
                        
                        self.accuracy_history.append(accuracy)
                        evaluated_count += 1
                        
                        logger.info(f"📊 {pred['ticker']} HFT accuracy: {accuracy:.1f}%")
                
                except Exception as e:
                    logger.error(f"❌ Error evaluating prediction {pred['id']}: {e}")
            
            return evaluated_count
            
        except Exception as e:
            logger.error(f"❌ Error in rapid evaluation: {e}")
            return 0
    
    async def _sentiment_refresh_loop(self):
        """
        Refresh sentiment data every 24 hours per stock
        """
        while self.is_running:
            try:
                for ticker in self.hft_stocks:
                    if self._needs_sentiment_refresh(ticker):
                        # Refresh sentiment in background
                        sentiment = await self._refresh_sentiment_data(ticker)
                        if sentiment:
                            self.sentiment_cache[ticker] = sentiment
                            self.sentiment_timestamps[ticker] = datetime.now()
                            logger.info(f"📰 Sentiment refreshed for {ticker}")
                
                # Sleep for 1 hour before checking again
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"❌ Sentiment refresh error: {e}")
                await asyncio.sleep(1800)  # 30-minute retry
    
    async def _performance_monitoring_loop(self):
        """
        Monitor and log HFT performance metrics
        """
        while self.is_running:
            try:
                if self.accuracy_history:
                    recent_accuracy = sum(self.accuracy_history[-100:]) / min(100, len(self.accuracy_history))
                    overall_accuracy = sum(self.accuracy_history) / len(self.accuracy_history)
                    
                    logger.info(f"📈 HFT Performance - Recent: {recent_accuracy:.1f}%, Overall: {overall_accuracy:.1f}%")
                    logger.info(f"🎯 Total predictions: {self.total_predictions}, Cycles: {self.cycle_count}")
                
                # Sleep for 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"❌ Performance monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_prediction_accuracy(self, prediction: Dict) -> Optional[float]:
        """
        Calculate accuracy of a prediction by comparing with actual price
        """
        try:
            ticker = prediction['ticker']
            predicted_price = prediction.get('predicted_price', 0)
            current_price = prediction.get('current_price', 0)

            if not predicted_price or not current_price:
                return None

            # Get actual current price
            stock = yf.Ticker(ticker)
            hist = stock.history(period="1d", interval="1m")

            if hist.empty:
                return None

            actual_price = float(hist['Close'].iloc[-1])

            # Calculate accuracy based on price direction and magnitude
            predicted_change = predicted_price - current_price
            actual_change = actual_price - current_price

            # Direction accuracy (50% weight)
            direction_correct = (predicted_change > 0) == (actual_change > 0)
            direction_score = 100 if direction_correct else 0

            # Magnitude accuracy (50% weight)
            if actual_change != 0:
                magnitude_error = abs(predicted_change - actual_change) / abs(actual_change)
                magnitude_score = max(0, 100 - (magnitude_error * 100))
            else:
                magnitude_score = 50  # Neutral score for no change

            # Combined accuracy
            accuracy = (direction_score * 0.5) + (magnitude_score * 0.5)

            return min(100, max(0, accuracy))

        except Exception as e:
            logger.error(f"❌ Error calculating accuracy: {e}")
            return None

    async def _update_prediction_accuracy(self, prediction_id: int, accuracy: float):
        """
        Update prediction with calculated accuracy
        """
        try:
            query = """
                UPDATE neural_network_predictions
                SET accuracy_score = %s,
                    evaluated_at = %s
                WHERE id = %s
            """

            self.db_service.execute_query(
                query,
                (accuracy, datetime.now(), prediction_id)
            )

        except Exception as e:
            logger.error(f"❌ Error updating prediction accuracy: {e}")

    async def _apply_learning_feedback(self, prediction: Dict, accuracy: float):
        """
        Apply learning feedback to neural network based on prediction accuracy
        """
        try:
            # Reward/punishment based on accuracy
            if accuracy >= 70:
                # Good prediction - reinforce
                reward = (accuracy - 50) / 50  # Scale 0-1
                logger.info(f"🎯 Rewarding model for {prediction['ticker']}: {reward:.2f}")
            else:
                # Poor prediction - punish
                punishment = (50 - accuracy) / 50  # Scale 0-1
                logger.info(f"⚠️ Punishing model for {prediction['ticker']}: {punishment:.2f}")

            # Store learning feedback for neural network improvement
            # This would integrate with the neural network's learning mechanism

        except Exception as e:
            logger.error(f"❌ Error applying learning feedback: {e}")

    async def _refresh_sentiment_data(self, ticker: str) -> Optional[Dict]:
        """
        Refresh sentiment data for a ticker
        """
        try:
            # This would integrate with sentiment analysis service
            # For now, return mock sentiment data
            sentiment_data = {
                'sentiment_score': 0.0,  # Neutral
                'news_count': 0,
                'last_updated': datetime.now()
            }

            logger.info(f"📰 Refreshed sentiment for {ticker}")
            return sentiment_data

        except Exception as e:
            logger.error(f"❌ Error refreshing sentiment for {ticker}: {e}")
            return None

    async def _update_learning_metrics(self):
        """
        Update learning metrics and performance tracking
        """
        try:
            if len(self.accuracy_history) >= 10:
                # Calculate recent performance trends
                recent_10 = self.accuracy_history[-10:]
                recent_avg = sum(recent_10) / len(recent_10)

                # Adjust learning rate based on performance
                if recent_avg > 75:
                    self.learning_rate = min(0.02, self.learning_rate * 1.1)  # Increase learning rate
                elif recent_avg < 50:
                    self.learning_rate = max(0.005, self.learning_rate * 0.9)  # Decrease learning rate

                logger.info(f"📊 Learning rate adjusted to: {self.learning_rate:.4f}")

        except Exception as e:
            logger.error(f"❌ Error updating learning metrics: {e}")

    def stop_hft_mode(self):
        """
        Stop HFT mode gracefully
        """
        logger.info("🛑 Stopping HFT Mode...")
        self.is_running = False
        self.executor.shutdown(wait=True)
        logger.info("✅ HFT Mode stopped")
