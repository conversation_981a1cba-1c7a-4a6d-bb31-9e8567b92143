"""
StockTrek Data Export Service
Comprehensive data collection and export system for investor presentations
"""

import os
import json
import csv
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import zipfile
from pathlib import Path

from Databases.database_service import DatabaseService

logger = logging.getLogger(__name__)

class DataExportService:
    """Comprehensive data export service for investor presentations"""
    
    def __init__(self):
        self.db_service = DatabaseService()
        self.export_dir = Path("exports")
        self.export_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.export_dir / "reports").mkdir(exist_ok=True)
        (self.export_dir / "raw_data").mkdir(exist_ok=True)
        (self.export_dir / "logs").mkdir(exist_ok=True)
        (self.export_dir / "charts").mkdir(exist_ok=True)
        
        logger.info("📊 Data Export Service initialized")
    
    def export_all_data(self, days_back: int = 30) -> str:
        """Export all system data for investor presentation"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_name = f"stocktrek_complete_export_{timestamp}"
        export_path = self.export_dir / export_name
        export_path.mkdir(exist_ok=True)
        
        logger.info(f"🚀 Starting complete data export: {export_name}")
        
        # Export all data types
        self.export_predictions_data(export_path, days_back)
        self.export_performance_metrics(export_path, days_back)
        self.export_learning_feedback(export_path, days_back)
        self.export_company_data(export_path)
        self.export_system_logs(export_path, days_back)
        self.export_daemon_activity(export_path, days_back)
        self.generate_summary_report(export_path, days_back)
        
        # Create ZIP archive
        zip_path = self.create_zip_archive(export_path, export_name)
        
        logger.info(f"✅ Complete export finished: {zip_path}")
        return str(zip_path)
    
    def export_predictions_data(self, export_path: Path, days_back: int):
        """Export all predictions data"""
        logger.info("📈 Exporting predictions data...")
        
        query = """
            SELECT 
                p.id,
                p.prediction_date,
                p.target_date,
                c.ticker,
                c.name as company_name,
                p.prediction_type,
                p.confidence_score,
                p.adjusted_confidence_score,
                p.predicted_price_change,
                p.current_price,
                p.predicted_price,
                p.price_change_percent,
                p.timeframe_days,
                p.is_hft,
                p.data_quality,
                p.confidence_multiplier,
                p.prediction_reliability,
                p.actual_outcome,
                p.actual_price_change,
                p.prediction_accuracy,
                p.reward_score,
                p.accuracy_score,
                p.model_version,
                p.evaluated_at,
                p.created_at,
                p.features_used,
                p.prediction_metadata,
                p.missing_data_flags,
                p.raw_prediction_data
            FROM neural_network_predictions p
            JOIN companies c ON p.company_id = c.id
            WHERE p.prediction_date >= NOW() - INTERVAL %s
            ORDER BY p.prediction_date DESC
        """
        
        results = self.db_service.execute_query(query, (f"{days_back} days",), fetch=True)
        
        if results:
            # Convert to DataFrame for better handling
            df = pd.DataFrame([dict(row) for row in results])
            
            # Export to multiple formats
            df.to_csv(export_path / "predictions_complete.csv", index=False)
            df.to_json(export_path / "predictions_complete.json", orient="records", indent=2, date_format='iso')
            df.to_excel(export_path / "predictions_complete.xlsx", index=False)
            
            # Create HFT-specific export
            hft_df = df[df['is_hft'] == True]
            if not hft_df.empty:
                hft_df.to_csv(export_path / "predictions_hft_only.csv", index=False)
                hft_df.to_json(export_path / "predictions_hft_only.json", orient="records", indent=2, date_format='iso')
            
            logger.info(f"   ✅ Exported {len(df)} predictions ({len(hft_df)} HFT)")
        else:
            logger.warning("   ⚠️ No predictions data found")
    
    def export_performance_metrics(self, export_path: Path, days_back: int):
        """Export performance and accuracy metrics"""
        logger.info("📊 Exporting performance metrics...")
        
        # Overall performance query
        perf_query = """
            SELECT 
                DATE(prediction_date) as date,
                COUNT(*) as total_predictions,
                COUNT(CASE WHEN is_hft THEN 1 END) as hft_predictions,
                COUNT(CASE WHEN NOT is_hft THEN 1 END) as regular_predictions,
                COUNT(CASE WHEN prediction_accuracy THEN 1 END) as accurate_predictions,
                AVG(CASE WHEN prediction_accuracy THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                AVG(confidence_score) as avg_confidence,
                AVG(reward_score) as avg_reward_score,
                COUNT(CASE WHEN is_hft AND prediction_accuracy THEN 1 END) as hft_accurate,
                COUNT(CASE WHEN NOT is_hft AND prediction_accuracy THEN 1 END) as regular_accurate
            FROM neural_network_predictions
            WHERE prediction_date >= NOW() - INTERVAL %s
            AND actual_outcome IS NOT NULL
            GROUP BY DATE(prediction_date)
            ORDER BY date DESC
        """
        
        results = self.db_service.execute_query(perf_query, (f"{days_back} days",), fetch=True)
        
        if results:
            df = pd.DataFrame([dict(row) for row in results])
            
            # Calculate additional metrics
            df['hft_accuracy_rate'] = df['hft_accurate'] / df['hft_predictions'].replace(0, 1)
            df['regular_accuracy_rate'] = df['regular_accurate'] / df['regular_predictions'].replace(0, 1)
            
            df.to_csv(export_path / "performance_daily.csv", index=False)
            df.to_json(export_path / "performance_daily.json", orient="records", indent=2, date_format='iso')
            
            logger.info(f"   ✅ Exported {len(df)} days of performance data")
        else:
            logger.warning("   ⚠️ No performance data found")
    
    def export_learning_feedback(self, export_path: Path, days_back: int):
        """Export learning feedback and model improvement data"""
        logger.info("🧠 Exporting learning feedback...")
        
        query = """
            SELECT 
                lf.*,
                p.prediction_date,
                p.is_hft,
                c.ticker
            FROM learning_feedback lf
            JOIN neural_network_predictions p ON lf.prediction_id = p.id
            JOIN companies c ON p.company_id = c.id
            WHERE lf.evaluation_date >= NOW() - INTERVAL %s
            ORDER BY lf.evaluation_date DESC
        """
        
        results = self.db_service.execute_query(query, (f"{days_back} days",), fetch=True)
        
        if results:
            df = pd.DataFrame([dict(row) for row in results])
            df.to_csv(export_path / "learning_feedback.csv", index=False)
            df.to_json(export_path / "learning_feedback.json", orient="records", indent=2, date_format='iso')
            
            logger.info(f"   ✅ Exported {len(df)} learning feedback records")
        else:
            logger.warning("   ⚠️ No learning feedback found")
    
    def export_company_data(self, export_path: Path):
        """Export company and stock data"""
        logger.info("🏢 Exporting company data...")
        
        # Companies
        companies_query = "SELECT * FROM companies ORDER BY ticker"
        companies = self.db_service.execute_query(companies_query, fetch=True)
        
        if companies:
            df = pd.DataFrame([dict(row) for row in companies])
            df.to_csv(export_path / "companies.csv", index=False)
            df.to_json(export_path / "companies.json", orient="records", indent=2, date_format='iso')
            
            logger.info(f"   ✅ Exported {len(df)} companies")
        
        # Company data (recent)
        data_query = """
            SELECT cd.*, c.ticker, c.name
            FROM company_data cd
            JOIN companies c ON cd.company_id = c.id
            WHERE cd.as_of_date >= NOW() - INTERVAL '30 days'
            ORDER BY cd.as_of_date DESC, c.ticker
        """
        
        company_data = self.db_service.execute_query(data_query, fetch=True)
        
        if company_data:
            df = pd.DataFrame([dict(row) for row in company_data])
            df.to_csv(export_path / "company_data_recent.csv", index=False)
            df.to_json(export_path / "company_data_recent.json", orient="records", indent=2, date_format='iso')
            
            logger.info(f"   ✅ Exported {len(df)} company data records")

    def export_system_logs(self, export_path: Path, days_back: int):
        """Export system logs for verification"""
        logger.info("📋 Exporting system logs...")

        log_files = [
            "logs/daemon.log",
            "stocktrek.log",
            "logs/predictions.log",
            "logs/hft_training.log"
        ]

        logs_dir = export_path / "logs"
        logs_dir.mkdir(exist_ok=True)

        cutoff_date = datetime.now() - timedelta(days=days_back)

        for log_file in log_files:
            if os.path.exists(log_file):
                try:
                    # Copy recent log entries
                    with open(log_file, 'r') as f:
                        lines = f.readlines()

                    # Filter by date (basic filtering)
                    recent_lines = []
                    for line in lines:
                        if any(str(cutoff_date.year) in line and
                              f"{cutoff_date.month:02d}" in line for _ in [1]):
                            recent_lines.append(line)
                        elif len(recent_lines) > 0:  # Keep all lines after we start finding recent ones
                            recent_lines.append(line)

                    # Write filtered logs
                    output_file = logs_dir / os.path.basename(log_file)
                    with open(output_file, 'w') as f:
                        f.writelines(recent_lines if recent_lines else lines[-1000:])  # Last 1000 lines if no date filtering

                    logger.info(f"   ✅ Exported {os.path.basename(log_file)}")
                except Exception as e:
                    logger.warning(f"   ⚠️ Could not export {log_file}: {e}")

    def export_daemon_activity(self, export_path: Path, days_back: int):
        """Export daemon activity and health metrics"""
        logger.info("🤖 Exporting daemon activity...")

        # Create daemon activity summary
        activity_data = {
            "export_timestamp": datetime.now().isoformat(),
            "export_period_days": days_back,
            "daemon_status": self.get_daemon_status(),
            "system_health": self.get_system_health(),
            "prediction_summary": self.get_prediction_summary(days_back),
            "hft_performance": self.get_hft_performance(days_back),
            "learning_progress": self.get_learning_progress(days_back)
        }

        with open(export_path / "daemon_activity.json", 'w') as f:
            json.dump(activity_data, f, indent=2, default=str)

        logger.info("   ✅ Exported daemon activity summary")

    def generate_summary_report(self, export_path: Path, days_back: int):
        """Generate executive summary report"""
        logger.info("📄 Generating summary report...")

        summary = self.create_executive_summary(days_back)

        # Write as JSON
        with open(export_path / "executive_summary.json", 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        # Write as readable text report
        with open(export_path / "INVESTOR_REPORT.txt", 'w') as f:
            f.write(self.format_investor_report(summary))

        logger.info("   ✅ Generated executive summary and investor report")

    def create_zip_archive(self, export_path: Path, export_name: str) -> Path:
        """Create ZIP archive of all exported data"""
        zip_path = self.export_dir / f"{export_name}.zip"

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in export_path.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(export_path)
                    zipf.write(file_path, arcname)

        return zip_path

    def get_daemon_status(self) -> Dict[str, Any]:
        """Get current daemon status"""
        try:
            import psutil
            pid_file = "daemon.pid"

            if os.path.exists(pid_file):
                with open(pid_file, 'r') as f:
                    pid = int(f.read().strip())

                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    return {
                        "status": "running",
                        "pid": pid,
                        "cpu_percent": process.cpu_percent(),
                        "memory_mb": process.memory_info().rss / 1024 / 1024,
                        "start_time": datetime.fromtimestamp(process.create_time()).isoformat()
                    }

            return {"status": "not_running"}
        except Exception as e:
            return {"status": "unknown", "error": str(e)}

    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics"""
        try:
            # Database connectivity
            db_test = self.db_service.execute_query("SELECT 1", fetch=True)
            db_healthy = bool(db_test)

            # Recent activity
            recent_query = """
                SELECT COUNT(*) as recent_predictions
                FROM neural_network_predictions
                WHERE prediction_date >= NOW() - INTERVAL '24 hours'
            """
            recent_result = self.db_service.execute_query(recent_query, fetch=True)
            recent_predictions = recent_result[0][0] if recent_result else 0

            return {
                "database_healthy": db_healthy,
                "recent_predictions_24h": recent_predictions,
                "last_health_check": datetime.now().isoformat()
            }
        except Exception as e:
            return {"healthy": False, "error": str(e)}

    def get_prediction_summary(self, days_back: int) -> Dict[str, Any]:
        """Get prediction summary statistics"""
        try:
            query = """
                SELECT
                    COUNT(*) as total_predictions,
                    COUNT(CASE WHEN is_hft THEN 1 END) as hft_predictions,
                    COUNT(CASE WHEN prediction_accuracy THEN 1 END) as accurate_predictions,
                    AVG(CASE WHEN prediction_accuracy THEN 1.0 ELSE 0.0 END) as accuracy_rate,
                    AVG(confidence_score) as avg_confidence,
                    COUNT(DISTINCT company_id) as companies_predicted
                FROM neural_network_predictions
                WHERE prediction_date >= NOW() - INTERVAL %s
                AND actual_outcome IS NOT NULL
            """

            result = self.db_service.execute_query(query, (f"{days_back} days",), fetch=True)

            if result and result[0]:
                row = result[0]
                return {
                    "total_predictions": row[0] or 0,
                    "hft_predictions": row[1] or 0,
                    "accurate_predictions": row[2] or 0,
                    "accuracy_rate": float(row[3] or 0),
                    "avg_confidence": float(row[4] or 0),
                    "companies_predicted": row[5] or 0
                }

            return {"total_predictions": 0, "accuracy_rate": 0.0}
        except Exception as e:
            logger.error(f"Error getting prediction summary: {e}")
            return {"error": str(e)}

    def get_hft_performance(self, days_back: int) -> Dict[str, Any]:
        """Get HFT-specific performance metrics"""
        try:
            query = """
                SELECT
                    COUNT(*) as hft_total,
                    COUNT(CASE WHEN prediction_accuracy THEN 1 END) as hft_accurate,
                    AVG(CASE WHEN prediction_accuracy THEN 1.0 ELSE 0.0 END) as hft_accuracy_rate,
                    AVG(confidence_score) as hft_avg_confidence,
                    AVG(reward_score) as hft_avg_reward
                FROM neural_network_predictions
                WHERE prediction_date >= NOW() - INTERVAL %s
                AND is_hft = true
                AND actual_outcome IS NOT NULL
            """

            result = self.db_service.execute_query(query, (f"{days_back} days",), fetch=True)

            if result and result[0]:
                row = result[0]
                return {
                    "hft_total_predictions": row[0] or 0,
                    "hft_accurate_predictions": row[1] or 0,
                    "hft_accuracy_rate": float(row[2] or 0),
                    "hft_avg_confidence": float(row[3] or 0),
                    "hft_avg_reward": float(row[4] or 0)
                }

            return {"hft_total_predictions": 0, "hft_accuracy_rate": 0.0}
        except Exception as e:
            return {"error": str(e)}

    def get_learning_progress(self, days_back: int) -> Dict[str, Any]:
        """Get learning and improvement metrics"""
        try:
            query = """
                SELECT
                    COUNT(*) as feedback_records,
                    AVG(price_error) as avg_price_error,
                    COUNT(CASE WHEN direction_correct THEN 1 END) as direction_correct_count,
                    AVG(CASE WHEN direction_correct THEN 1.0 ELSE 0.0 END) as direction_accuracy,
                    AVG(confidence) as avg_feedback_confidence
                FROM learning_feedback
                WHERE evaluation_date >= NOW() - INTERVAL %s
            """

            result = self.db_service.execute_query(query, (f"{days_back} days",), fetch=True)

            if result and result[0]:
                row = result[0]
                return {
                    "feedback_records": row[0] or 0,
                    "avg_price_error": float(row[1] or 0),
                    "direction_correct_count": row[2] or 0,
                    "direction_accuracy": float(row[3] or 0),
                    "avg_feedback_confidence": float(row[4] or 0)
                }

            return {"feedback_records": 0, "direction_accuracy": 0.0}
        except Exception as e:
            return {"error": str(e)}

    def create_executive_summary(self, days_back: int) -> Dict[str, Any]:
        """Create executive summary for investors"""
        prediction_summary = self.get_prediction_summary(days_back)
        hft_performance = self.get_hft_performance(days_back)
        learning_progress = self.get_learning_progress(days_back)
        daemon_status = self.get_daemon_status()
        system_health = self.get_system_health()

        return {
            "report_metadata": {
                "generated_at": datetime.now().isoformat(),
                "period_days": days_back,
                "report_type": "investor_verification",
                "system_version": "StockTrek v1.0"
            },
            "system_status": {
                "daemon_operational": daemon_status.get("status") == "running",
                "database_healthy": system_health.get("database_healthy", False),
                "recent_activity": system_health.get("recent_predictions_24h", 0) > 0
            },
            "performance_metrics": prediction_summary,
            "hft_metrics": hft_performance,
            "learning_metrics": learning_progress,
            "operational_data": {
                "daemon_status": daemon_status,
                "system_health": system_health
            }
        }

    def format_investor_report(self, summary: Dict[str, Any]) -> str:
        """Format executive summary as readable investor report"""
        report = []
        report.append("=" * 80)
        report.append("STOCKTREK AUTONOMOUS TRADING SYSTEM")
        report.append("INVESTOR VERIFICATION REPORT")
        report.append("=" * 80)
        report.append("")

        # Report metadata
        metadata = summary.get("report_metadata", {})
        report.append(f"Report Generated: {metadata.get('generated_at', 'Unknown')}")
        report.append(f"Analysis Period: {metadata.get('period_days', 0)} days")
        report.append(f"System Version: {metadata.get('system_version', 'Unknown')}")
        report.append("")

        # System status
        status = summary.get("system_status", {})
        report.append("SYSTEM OPERATIONAL STATUS")
        report.append("-" * 40)
        report.append(f"Daemon Running: {'✅ YES' if status.get('daemon_operational') else '❌ NO'}")
        report.append(f"Database Healthy: {'✅ YES' if status.get('database_healthy') else '❌ NO'}")
        report.append(f"Recent Activity: {'✅ YES' if status.get('recent_activity') else '❌ NO'}")
        report.append("")

        # Performance metrics
        perf = summary.get("performance_metrics", {})
        report.append("PREDICTION PERFORMANCE")
        report.append("-" * 40)
        report.append(f"Total Predictions: {perf.get('total_predictions', 0):,}")
        report.append(f"Accurate Predictions: {perf.get('accurate_predictions', 0):,}")
        report.append(f"Overall Accuracy: {perf.get('accuracy_rate', 0):.1%}")
        report.append(f"Average Confidence: {perf.get('avg_confidence', 0):.1f}%")
        report.append(f"Companies Analyzed: {perf.get('companies_predicted', 0):,}")
        report.append("")

        # HFT metrics
        hft = summary.get("hft_metrics", {})
        report.append("HIGH-FREQUENCY TRADING PERFORMANCE")
        report.append("-" * 40)
        report.append(f"HFT Predictions: {hft.get('hft_total_predictions', 0):,}")
        report.append(f"HFT Accurate: {hft.get('hft_accurate_predictions', 0):,}")
        report.append(f"HFT Accuracy Rate: {hft.get('hft_accuracy_rate', 0):.1%}")
        report.append(f"HFT Avg Confidence: {hft.get('hft_avg_confidence', 0):.1f}%")
        report.append(f"HFT Avg Reward Score: {hft.get('hft_avg_reward', 0):.2f}")
        report.append("")

        # Learning metrics
        learning = summary.get("learning_metrics", {})
        report.append("MACHINE LEARNING PROGRESS")
        report.append("-" * 40)
        report.append(f"Learning Feedback Records: {learning.get('feedback_records', 0):,}")
        report.append(f"Direction Accuracy: {learning.get('direction_accuracy', 0):.1%}")
        report.append(f"Average Price Error: {learning.get('avg_price_error', 0):.2f}%")
        report.append("")

        report.append("=" * 80)
        report.append("This report verifies the operational status and performance")
        report.append("of the StockTrek autonomous trading system for investor review.")
        report.append("=" * 80)

        return "\n".join(report)
