# 🚀 StockTrek - Autonomous Stock Prediction System

**Enterprise-grade autonomous stock prediction system with HFT capabilities, real-time data processing, and bulletproof data quality.**

## ⚡ Quick Start

### Start Production System (HFT Mode)
```bash
# Navigate to StockTrek directory
cd ~/Desktop/StockTrek

# Start HFT autonomous system
./scripts/start_stocktrek.sh

# Or manually:
python3 main.py --hft
```

### Check System Status
```bash
python3 scripts/check_status.py
```

## 🎯 Key Features

✅ **High-Frequency Trading Mode** - 5-minute prediction cycles with rapid learning  
✅ **Zero Dummy Data** - Real market data only, refuses predictions without valid data  
✅ **24/7 Autonomous Operation** - Continuous scanning and prediction  
✅ **Desktop Data Export** - Automatic compilation every 4 hours to `~/Desktop/StockTrek Data`  
✅ **Self-Improvement** - Reward/punishment based on prediction accuracy  
✅ **Anomaly Detection** - AI-powered prediction validation  
✅ **PostgreSQL Integration** - Enterprise database with data quality tracking  

## 📊 Usage Examples

### Individual Predictions
```bash
# Predict AAPL for 7 days
python3 main.py --predict AAPL --timeframe 7

# HFT prediction (5 minutes)
python3 main.py --predict AAPL --hft --timeframe 5

# Multiple stocks
python3 main.py --predict AAPL,MSFT,GOOGL --timeframe 7
```

### System Modes
```bash
# High-Frequency Trading (recommended)
python3 main.py --hft

# Standard autonomous mode
python3 main.py

# Slow/conservative mode
python3 main.py --slow

# Parallel training mode
python3 main.py --parallel
```

### Data Export
```bash
# Complete export (30 days)
python3 main.py --export complete

# Quick report (7 days)
python3 main.py --export quick

# Investor presentation package
python3 main.py --export investor
```

### Monitoring
```bash
# System status
python3 main.py --status

# Backtesting results
python3 main.py --backtest

# Anomaly dashboard
python3 main.py --dashboard

# Live database monitoring
python3 main.py --live
```

## 📁 Directory Structure

```
StockTrek/
├── 📂 Databases/          # Database services and schemas
├── 📂 ETL/               # Data processing and prediction services
├── 📂 Neural_Network/    # AI models and neural network code
├── 📂 Preprocessing/     # Data preprocessing modules
├── 📂 config/            # Configuration files
├── 📂 exports/           # Data export outputs
├── 📂 logs/              # System logs
├── 📂 docs/              # Documentation and guides
├── 📂 scripts/           # Utility and setup scripts
├── 📂 tests/             # Test and debug scripts
├── 📄 main.py            # Main entry point
└── 📄 requirements.txt   # Python dependencies
```

## 🔧 Requirements

- **Python 3.9+**
- **PostgreSQL** (running locally)
- **Required packages**: `pip3 install -r requirements.txt`

### Key Dependencies
- `yfinance` - Real-time market data
- `psycopg2-binary` - PostgreSQL integration
- `scikit-learn` - Machine learning models
- `pandas` - Data processing
- `numpy` - Numerical computations

## 📚 Documentation

- **[System Overview](docs/SYSTEM_OVERVIEW.md)** - Complete system architecture
- **[Manual Commands](docs/manual_commands.md)** - All available commands
- **[Data Export Guide](docs/INVESTOR_DATA_EXPORT_GUIDE.md)** - Export functionality
- **[Daemon Setup](docs/DAEMON_SETUP_COMPLETE.md)** - Background service setup

## 🛡️ Data Quality

StockTrek enforces **zero tolerance for dummy data**:
- All predictions use real market data from yfinance
- System refuses to predict when valid data unavailable
- No 100.0 fallback values or dummy prices
- Comprehensive data quality tracking and validation

## 🎯 Production Ready

- **Enterprise Architecture** - Modular, scalable design
- **24/7 Operation** - Persistent daemon with auto-restart
- **Data Integrity** - PostgreSQL with ACID compliance
- **Performance Monitoring** - Real-time accuracy tracking
- **Export Automation** - Scheduled investor reports
- **Error Handling** - Graceful failure and recovery

## 🚀 Getting Started

1. **Install dependencies**: `pip3 install -r requirements.txt`
2. **Start PostgreSQL**: Ensure database is running
3. **Run system check**: `python3 scripts/check_status.py`
4. **Start HFT mode**: `./scripts/start_stocktrek.sh`

**🎯 Your autonomous stock prediction system is ready for production!**
