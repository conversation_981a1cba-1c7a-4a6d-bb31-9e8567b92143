#!/usr/bin/env python3
"""
Clean up StockTrek environment while preserving functionality
"""

import os
import shutil
from pathlib import Path

def cleanup_environment():
    """Clean up the root directory organization"""
    print("🧹 Cleaning up StockTrek environment...")
    
    # Create organized directory structure
    directories_to_create = [
        'docs',
        'scripts',
        'tests',
        'temp'
    ]
    
    for dir_name in directories_to_create:
        os.makedirs(dir_name, exist_ok=True)
        print(f"📁 Created/verified directory: {dir_name}")
    
    # Files to move to docs/
    doc_files = [
        'DAEMON_GUIDE.md',
        'DAEMON_SETUP_COMPLETE.md', 
        'DATA_EXPORT_SYSTEM_COMPLETE.md',
        'INVESTOR_DATA_EXPORT_GUIDE.md',
        'SYSTEM_OVERVIEW.md',
        'manual_commands.md'
    ]
    
    # Files to move to scripts/
    script_files = [
        'setup_complete_system.py',
        'setup_daemon.sh',
        'start_stocktrek.sh',
        'check_status.py',
        'trigger_desktop_export.py',
        'export_data.py',
        'db_monitor.py',
        'daemon_service.py'
    ]
    
    # Files to move to tests/
    test_files = [
        'test_desktop_export.py',
        'test_hft_training.py', 
        'test_new_predictions.py',
        'test_sq_specifically.py',
        'quick_test_all.py',
        'debug_price_data.py',
        'check_database_prices.py',
        'cleanup_environment.py'  # This script itself
    ]
    
    # Move documentation files
    print("\n📚 Moving documentation files...")
    for file in doc_files:
        if os.path.exists(file):
            shutil.move(file, f'docs/{file}')
            print(f"   📄 Moved {file} → docs/")
    
    # Move script files
    print("\n🔧 Moving script files...")
    for file in script_files:
        if os.path.exists(file):
            shutil.move(file, f'scripts/{file}')
            print(f"   🔧 Moved {file} → scripts/")
    
    # Move test files
    print("\n🧪 Moving test files...")
    for file in test_files:
        if os.path.exists(file):
            shutil.move(file, f'tests/{file}')
            print(f"   🧪 Moved {file} → tests/")
    
    # Clean up temporary files
    print("\n🗑️  Cleaning temporary files...")
    temp_patterns = [
        '*.pyc',
        '*.log',
        '*.pid'
    ]
    
    for pattern in temp_patterns:
        import glob
        for file in glob.glob(pattern):
            if os.path.isfile(file):
                os.remove(file)
                print(f"   🗑️  Removed {file}")
    
    # Clean up __pycache__ directories
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                pycache_path = os.path.join(root, dir_name)
                shutil.rmtree(pycache_path)
                print(f"   🗑️  Removed {pycache_path}")
    
    print("\n✅ Environment cleanup complete!")
    print("\n📁 New directory structure:")
    print("   📂 StockTrek/")
    print("   ├── 📂 Databases/          # Database services and schemas")
    print("   ├── 📂 ETL/               # Data processing and prediction services")
    print("   ├── 📂 Neural_Network/    # AI models and neural network code")
    print("   ├── 📂 Preprocessing/     # Data preprocessing modules")
    print("   ├── 📂 config/            # Configuration files")
    print("   ├── 📂 exports/           # Data export outputs")
    print("   ├── 📂 logs/              # System logs")
    print("   ├── 📂 docs/              # Documentation and guides")
    print("   ├── 📂 scripts/           # Utility and setup scripts")
    print("   ├── 📂 tests/             # Test and debug scripts")
    print("   ├── 📄 main.py            # Main entry point")
    print("   └── 📄 requirements.txt   # Python dependencies")

if __name__ == "__main__":
    cleanup_environment()
