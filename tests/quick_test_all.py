#!/usr/bin/env python3
"""
Quick test all StockTrek functionality without waiting
"""

import subprocess
import sys
import time
from pathlib import Path

def test_prediction():
    """Test basic prediction functionality"""
    print("🧪 Testing Basic Prediction...")
    try:
        result = subprocess.run(
            [sys.executable, "main.py", "--predict", "AAPL", "--timeframe", "5"],
            timeout=60,
            capture_output=True,
            text=True
        )
        
        if "Prediction for AAPL" in result.stdout and "$" in result.stdout:
            print("✅ Basic prediction working - realistic prices generated")
            return True
        else:
            print("❌ Prediction failed or no price data")
            print(result.stdout[-200:])
            return False
            
    except Exception as e:
        print(f"❌ Prediction test failed: {e}")
        return False

def test_hft_quick():
    """Test HFT mode for just a few seconds"""
    print("🧪 Testing HFT Mode (10 second test)...")
    try:
        # Start HFT mode
        process = subprocess.Popen(
            [sys.executable, "main.py", "--hft"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Let it run for 10 seconds
        time.sleep(10)
        
        # Terminate and check output
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        if "HFT" in stdout or "prediction" in stdout.lower():
            print("✅ HFT mode started successfully")
            return True
        else:
            print("⚠️ HFT mode may have issues:")
            print(stdout[-200:] if stdout else "No output")
            return False
            
    except Exception as e:
        print(f"❌ HFT test failed: {e}")
        return False

def test_daemon_status():
    """Test daemon status and control"""
    print("🧪 Testing Daemon Status...")
    try:
        result = subprocess.run(
            [sys.executable, "daemon_service.py", "status"],
            capture_output=True,
            text=True
        )
        
        if "running" in result.stdout.lower() or "not running" in result.stdout.lower():
            print("✅ Daemon status command working")
            return True
        else:
            print("⚠️ Daemon status unclear:")
            print(result.stdout)
            return True  # Still counts as working
            
    except Exception as e:
        print(f"❌ Daemon status test failed: {e}")
        return False

def test_database_quick():
    """Quick database connectivity test"""
    print("🧪 Testing Database Connection...")
    try:
        result = subprocess.run(
            ["psql", "-d", "stocktrek", "-c", "SELECT COUNT(*) FROM companies;"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and "count" in result.stdout:
            print("✅ Database connection working")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_export_quick():
    """Quick export test"""
    print("🧪 Testing Export Generation...")
    try:
        result = subprocess.run(
            [sys.executable, "main.py", "--export", "quick"],
            timeout=30,
            capture_output=True,
            text=True
        )
        
        if "EXPORT COMPLETE" in result.stdout:
            print("✅ Export generation working")
            return True
        else:
            print("❌ Export generation failed")
            print(result.stdout[-200:])
            return False
            
    except Exception as e:
        print(f"❌ Export test failed: {e}")
        return False

def check_desktop_files():
    """Check if desktop files are being created"""
    print("🧪 Checking Desktop Files...")
    try:
        desktop_path = Path.home() / "Desktop" / "StockTrek Data"
        if desktop_path.exists():
            files = list(desktop_path.glob("*.zip"))
            if files:
                latest_file = max(files, key=lambda x: x.stat().st_mtime)
                size_mb = latest_file.stat().st_size / 1024 / 1024
                print(f"✅ Desktop files present - Latest: {latest_file.name} ({size_mb:.2f} MB)")
                return True
            else:
                print("⚠️ Desktop folder exists but no files")
                return False
        else:
            print("❌ Desktop folder not created")
            return False
            
    except Exception as e:
        print(f"❌ Desktop check failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 StockTrek Quick Test Suite")
    print("=" * 50)
    print("Testing all functionality without waiting for schedules...")
    print()
    
    tests = [
        ("Basic Prediction", test_prediction),
        ("Database Connection", test_database_quick),
        ("Export Generation", test_export_quick),
        ("Desktop Files", check_desktop_files),
        ("HFT Mode", test_hft_quick),
        ("Daemon Status", test_daemon_status),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        results[test_name] = test_func()
    
    print("\n" + "=" * 50)
    print("🎯 QUICK TEST RESULTS:")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🏆 Score: {passed}/{total} tests passed")
    
    if passed >= total - 1:  # Allow 1 failure
        print("🎉 System is ready for production!")
        print("\n🚀 To start full autonomous operation:")
        print("   python3 daemon_service.py start")
        print("\n📊 To monitor in real-time:")
        print("   python3 db_monitor.py")
    else:
        print("⚠️ Some issues detected - check failed tests above")
