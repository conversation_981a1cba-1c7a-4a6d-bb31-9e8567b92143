#!/usr/bin/env python3
"""
Check what prices are actually stored in the database
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from Databases.database_service import DatabaseService

def check_database_prices():
    """Check what prices are in the database"""
    print("🔍 Checking database prices...")
    
    db = DatabaseService()
    
    try:
        # Get recent predictions
        query = """
        SELECT 
            c.ticker,
            c.name,
            p.current_price,
            p.predicted_price,
            p.prediction_date,
            p.data_quality,
            p.created_at
        FROM neural_network_predictions p
        JOIN companies c ON p.company_id = c.id
        ORDER BY p.created_at DESC
        LIMIT 20
        """
        
        results = db.execute_query(query, fetch=True)

        if results and len(results) > 0:
            print(f"\n📊 Found {len(results)} recent predictions:")
            print("-" * 80)
            for row in results:
                ticker = row['ticker']
                name = row['name']
                current_price = row['current_price']
                predicted_price = row['predicted_price']
                prediction_date = row['prediction_date']
                data_quality = row['data_quality']
                created_at = row['created_at']
                
                print(f"🏢 {ticker} ({name})")
                print(f"   💰 Current: ${current_price}")
                print(f"   🔮 Predicted: ${predicted_price}")
                print(f"   📅 Date: {prediction_date}")
                print(f"   📊 Quality: {data_quality}")
                print(f"   🕐 Created: {created_at}")
                
                # Flag suspicious prices
                if current_price == 100.0:
                    print(f"   🚨 SUSPICIOUS: Current price is exactly 100.0!")
                if predicted_price == 100.0:
                    print(f"   🚨 SUSPICIOUS: Predicted price is exactly 100.0!")
                if current_price == 2.0:
                    print(f"   🚨 SUSPICIOUS: Current price is exactly 2.0!")
                if predicted_price == 2.0:
                    print(f"   🚨 SUSPICIOUS: Predicted price is exactly 2.0!")
                    
                print()
        else:
            print("❌ No predictions found in database")
            
        # Check company data
        query2 = """
        SELECT 
            ticker,
            name,
            current_price,
            market_cap,
            pe_ratio,
            updated_at
        FROM companies
        ORDER BY updated_at DESC
        LIMIT 10
        """
        
        results2 = db.execute_query(query2, fetch=True)

        if results2 and len(results2) > 0:
            print(f"\n🏢 Found {len(results2)} companies in database:")
            print("-" * 80)
            for row in results2:
                ticker = row['ticker']
                name = row['name']
                current_price = row['current_price']
                market_cap = row['market_cap']
                pe_ratio = row['pe_ratio']
                updated_at = row['updated_at']
                
                print(f"🏢 {ticker} ({name})")
                print(f"   💰 Price: ${current_price}")
                print(f"   📊 Market Cap: {market_cap}")
                print(f"   📈 P/E: {pe_ratio}")
                print(f"   🕐 Updated: {updated_at}")
                
                # Flag suspicious prices
                if current_price == 100.0:
                    print(f"   🚨 SUSPICIOUS: Price is exactly 100.0!")
                if current_price == 2.0:
                    print(f"   🚨 SUSPICIOUS: Price is exactly 2.0!")
                    
                print()
        else:
            print("❌ No companies found in database")
            
    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 StockTrek Database Price Check")
    print("=" * 60)
    
    check_database_prices()
    
    print("🎯 Check Complete")
    print("=" * 60)
