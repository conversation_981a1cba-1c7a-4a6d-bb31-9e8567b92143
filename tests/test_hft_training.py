#!/usr/bin/env python3
"""
Test HFT training functionality directly
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from daemon_service import StockTrekDaemon

def test_hft_training():
    """Test the daemon's HFT training function directly"""
    print("🧪 Testing HFT Auto-Training Function")
    print("=" * 50)
    
    try:
        # Create daemon instance (without starting full daemon)
        daemon = StockTrekDaemon()
        
        print("⚡ Running HFT training cycle...")
        
        # Manually trigger HFT training (run one cycle)
        import subprocess
        import time

        # Run HFT mode for 30 seconds to simulate training
        print("⚡ Starting HFT mode for 30 seconds...")
        process = subprocess.Popen(
            [sys.executable, "main.py", "--hft"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # Let it run for 30 seconds
        time.sleep(30)

        # Terminate gracefully
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)

        if "prediction" in stdout.lower() or "hft" in stdout.lower():
            print("⚡ HFT training cycle generated predictions")
        else:
            print("⚡ HFT training cycle completed")
        
        print("✅ HFT training cycle completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ HFT training test failed: {e}")
        return False

if __name__ == "__main__":
    test_hft_training()
