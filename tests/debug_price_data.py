#!/usr/bin/env python3
"""
Debug price data to find where 100.0000 values are coming from
"""

import yfinance as yf
import sys
import os
sys.path.append(os.path.dirname(__file__))

from ETL.prediction_service import get_live_stock_data, PredictionService

def test_yfinance_directly():
    """Test yfinance directly for several stocks"""
    print("🔍 Testing yfinance directly...")
    
    test_symbols = ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'NVDA']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d")
            
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                print(f"   ✅ Current price: ${current_price:.2f}")
                print(f"   📈 Regular market: ${info.get('regularMarketPrice', 'N/A')}")
                print(f"   📉 Previous close: ${info.get('previousClose', 'N/A')}")
            else:
                print(f"   ❌ No historical data")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_get_live_stock_data():
    """Test our get_live_stock_data function"""
    print("\n🔍 Testing get_live_stock_data function...")
    
    test_symbols = ['AAPL', 'TSLA', 'MSFT']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        try:
            data = get_live_stock_data(symbol)
            if data:
                print(f"   ✅ Current price: ${data.get('current_price', 'N/A')}")
                print(f"   📈 Regular market: ${data.get('regular_market_price', 'N/A')}")
                print(f"   📉 Previous close: ${data.get('previous_close', 'N/A')}")
                print(f"   📊 Market cap: {data.get('market_cap', 'N/A')}")
                print(f"   🔢 Volume: {data.get('volume', 'N/A')}")
            else:
                print(f"   ❌ No data returned")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_prediction_service():
    """Test the full prediction service pipeline"""
    print("\n🔍 Testing PredictionService pipeline...")
    
    service = PredictionService()
    
    test_symbols = ['AAPL', 'TSLA']
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol} full pipeline:")
        try:
            # Test data processing
            company_data = service.process_company_data(symbol)
            
            print(f"   📋 Data quality: {company_data.get('data_quality', 'N/A')}")
            print(f"   💰 Current price: ${company_data.get('current_price', 'N/A')}")
            print(f"   💰 Price field: ${company_data.get('price', 'N/A')}")
            print(f"   📈 Regular market: ${company_data.get('regular_market_price', 'N/A')}")
            print(f"   📉 Previous close: ${company_data.get('previous_close', 'N/A')}")
            print(f"   ⚠️ Missing fields: {company_data.get('missing_fields', [])}")
            
            # Check if any field has 100.0
            for key, value in company_data.items():
                if isinstance(value, (int, float)) and value == 100.0:
                    print(f"   🚨 FOUND 100.0 in field '{key}': {value}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_neural_network_input():
    """Test what the neural network receives"""
    print("\n🔍 Testing Neural Network Input...")
    
    service = PredictionService()
    
    for symbol in ['AAPL']:
        print(f"\n🧠 Testing neural network input for {symbol}:")
        try:
            company_data = service.process_company_data(symbol)
            
            # Test neural network feature preparation
            features = service.neural_network.prepare_features(company_data)
            
            if features is not None:
                print(f"   ✅ Features prepared: {len(features)} features")
                print(f"   🔢 Feature values: {features[:10]}...")  # First 10 values
                
                # Check for 100.0 values in features
                if 100.0 in features:
                    print(f"   🚨 FOUND 100.0 in neural network features!")
                    indices = [i for i, x in enumerate(features) if x == 100.0]
                    print(f"   🚨 Indices with 100.0: {indices}")
            else:
                print(f"   ❌ Feature preparation failed")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 StockTrek Price Data Debug")
    print("=" * 60)
    
    test_yfinance_directly()
    test_get_live_stock_data()
    test_prediction_service()
    test_neural_network_input()
    
    print("\n🎯 Debug Complete")
    print("=" * 60)
