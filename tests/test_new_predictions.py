#!/usr/bin/env python3
"""
Test making new predictions to see if dummy data issue is fixed
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from ETL.prediction_service import PredictionService

def test_new_predictions():
    """Test making new predictions with current system"""
    print("🔍 Testing new predictions with fixed system...")
    
    service = PredictionService()
    
    # Test valid stocks
    valid_stocks = ['AAPL', 'MSFT', 'GOOGL']
    
    # Test invalid/delisted stocks  
    invalid_stocks = ['SQ', 'INVALID', 'FAKE123']
    
    print("\n📊 Testing VALID stocks:")
    print("-" * 40)
    
    for symbol in valid_stocks:
        print(f"\n🏢 Testing {symbol}:")
        try:
            prediction = service.make_prediction(symbol, timeframe_days=7)
            
            if prediction:
                current_price = prediction.get('current_price')
                predicted_price = prediction.get('predicted_price')
                confidence = prediction.get('confidence', 0)
                
                print(f"   ✅ Prediction made")
                print(f"   💰 Current: ${current_price}")
                print(f"   🔮 Predicted: ${predicted_price}")
                print(f"   📊 Confidence: {confidence:.1f}%")
                
                # Check for dummy data
                if current_price == 100.0:
                    print(f"   🚨 DUMMY DATA: Current price is 100.0!")
                if predicted_price == 100.0:
                    print(f"   🚨 DUMMY DATA: Predicted price is 100.0!")
                if current_price == 2.0:
                    print(f"   🚨 DUMMY DATA: Current price is 2.0!")
                    
            else:
                print(f"   ❌ No prediction returned")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n📊 Testing INVALID stocks:")
    print("-" * 40)
    
    for symbol in invalid_stocks:
        print(f"\n🏢 Testing {symbol}:")
        try:
            prediction = service.make_prediction(symbol, timeframe_days=7)
            
            if prediction:
                current_price = prediction.get('current_price')
                predicted_price = prediction.get('predicted_price')
                confidence = prediction.get('confidence', 0)
                
                print(f"   🚨 UNEXPECTED: Prediction made for invalid stock!")
                print(f"   💰 Current: ${current_price}")
                print(f"   🔮 Predicted: ${predicted_price}")
                print(f"   📊 Confidence: {confidence:.1f}%")
                
                # Check for dummy data
                if current_price == 100.0:
                    print(f"   🚨 DUMMY DATA: Current price is 100.0!")
                if predicted_price == 100.0:
                    print(f"   🚨 DUMMY DATA: Predicted price is 100.0!")
                    
            else:
                print(f"   ✅ Correctly refused prediction")
                
        except Exception as e:
            print(f"   ✅ Correctly failed: {e}")

if __name__ == "__main__":
    print("🚀 New Predictions Test")
    print("=" * 60)
    
    test_new_predictions()
    
    print("\n🎯 Test Complete")
    print("=" * 60)
