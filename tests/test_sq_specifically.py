#!/usr/bin/env python3
"""
Test SQ specifically to see why it's getting 100.0 dummy data
"""

import yfinance as yf
import sys
import os
sys.path.append(os.path.dirname(__file__))

from ETL.prediction_service import get_live_stock_data, PredictionService

def test_sq_yfinance():
    """Test SQ directly with yfinance"""
    print("🔍 Testing SQ with yfinance directly...")
    
    try:
        ticker = yf.Ticker("SQ")
        info = ticker.info
        hist = ticker.history(period="1d")
        
        print(f"📊 SQ Info keys: {list(info.keys())[:20]}...")
        print(f"📈 Regular market price: {info.get('regularMarketPrice', 'N/A')}")
        print(f"📉 Previous close: {info.get('previousClose', 'N/A')}")
        print(f"💰 Current price: {info.get('currentPrice', 'N/A')}")
        print(f"📊 Market cap: {info.get('marketCap', 'N/A')}")
        
        if not hist.empty:
            current_price = hist['Close'].iloc[-1]
            print(f"📈 Historical close: ${current_price:.2f}")
            print(f"📊 Volume: {hist['Volume'].iloc[-1]}")
        else:
            print("❌ No historical data")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_sq_get_live_data():
    """Test SQ with our get_live_stock_data function"""
    print("\n🔍 Testing SQ with get_live_stock_data...")
    
    try:
        data = get_live_stock_data("SQ")
        if data:
            print(f"✅ Data returned: {len(data)} fields")
            for key, value in data.items():
                print(f"   {key}: {value}")
                if isinstance(value, (int, float)) and value == 100.0:
                    print(f"   🚨 FOUND 100.0 in field '{key}'!")
        else:
            print("❌ No data returned")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_sq_full_pipeline():
    """Test SQ through full prediction pipeline"""
    print("\n🔍 Testing SQ through full pipeline...")
    
    try:
        service = PredictionService()
        company_data = service.process_company_data("SQ")
        
        print(f"📋 Data quality: {company_data.get('data_quality', 'N/A')}")
        print(f"⚠️ Missing fields: {company_data.get('missing_fields', [])}")
        
        # Check all price-related fields
        price_fields = ['current_price', 'price', 'regular_market_price', 'previous_close']
        for field in price_fields:
            value = company_data.get(field)
            print(f"💰 {field}: {value}")
            if isinstance(value, (int, float)) and value == 100.0:
                print(f"   🚨 FOUND 100.0 in field '{field}'!")
                
        # Check for any 100.0 values
        print("\n🔍 Scanning all fields for 100.0:")
        for key, value in company_data.items():
            if isinstance(value, (int, float)) and value == 100.0:
                print(f"   🚨 FOUND 100.0 in field '{key}': {value}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_sq_neural_network():
    """Test what SQ data looks like to neural network"""
    print("\n🔍 Testing SQ neural network input...")
    
    try:
        service = PredictionService()
        company_data = service.process_company_data("SQ")
        
        # Test neural network feature preparation
        features = service.neural_network.prepare_features(company_data)
        
        if features is not None:
            print(f"✅ Features prepared: {len(features)} features")
            print(f"🔢 Feature values: {features}")
            
            # Check for 100.0 values in features
            if 100.0 in features:
                print(f"🚨 FOUND 100.0 in neural network features!")
                indices = [i for i, x in enumerate(features) if x == 100.0]
                print(f"🚨 Indices with 100.0: {indices}")
        else:
            print(f"❌ Feature preparation failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 SQ Specific Debug")
    print("=" * 60)
    
    test_sq_yfinance()
    test_sq_get_live_data()
    test_sq_full_pipeline()
    test_sq_neural_network()
    
    print("\n🎯 SQ Debug Complete")
    print("=" * 60)
