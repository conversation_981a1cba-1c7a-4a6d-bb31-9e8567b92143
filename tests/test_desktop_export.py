#!/usr/bin/env python3
"""
Test Desktop Export Functionality
Manually trigger the desktop export to test without waiting 4 hours
"""

import sys
import subprocess
import shutil
from pathlib import Path
from datetime import datetime

def test_desktop_export():
    """Test the desktop export functionality immediately"""
    print("🧪 Testing Desktop Export Functionality")
    print("=" * 50)
    
    try:
        # Create desktop folder
        desktop_path = Path.home() / "Desktop" / "StockTrek Data"
        desktop_path.mkdir(exist_ok=True)
        print(f"📁 Desktop folder created: {desktop_path}")

        # Generate export
        print("📊 Generating export...")
        result = subprocess.run(
            [sys.executable, "main.py", "--export", "complete", "--export-days", "7"],
            capture_output=True,
            text=True
        )

        if result.returncode == 0:
            print("✅ Export generated successfully")
            
            # Find the latest export file
            exports_dir = Path("exports")
            if exports_dir.exists():
                zip_files = list(exports_dir.glob("stocktrek_complete_export_*.zip"))
                if zip_files:
                    latest_export = max(zip_files, key=lambda x: x.stat().st_mtime)
                    print(f"📦 Latest export found: {latest_export}")

                    # Copy to desktop with timestamp
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
                    desktop_file = desktop_path / f"StockTrek_Export_{timestamp}.zip"
                    shutil.copy2(latest_export, desktop_file)

                    print(f"✅ Export copied to desktop: {desktop_file}")
                    print(f"📁 File size: {desktop_file.stat().st_size / 1024 / 1024:.2f} MB")
                    
                    # List desktop contents
                    print("\n📋 Desktop StockTrek Data folder contents:")
                    for file in desktop_path.iterdir():
                        if file.is_file():
                            size_mb = file.stat().st_size / 1024 / 1024
                            print(f"   📄 {file.name} ({size_mb:.2f} MB)")
                    
                    return True
                else:
                    print("❌ No export files found")
                    return False
            else:
                print("❌ Exports directory not found")
                return False
        else:
            print(f"❌ Export generation failed: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ Desktop export test failed: {e}")
        return False

def test_hft_mode():
    """Test HFT mode functionality"""
    print("\n🧪 Testing HFT Mode")
    print("=" * 50)
    
    try:
        print("⚡ Running HFT mode for 30 seconds...")
        result = subprocess.run(
            [sys.executable, "main.py", "--hft"],
            timeout=30,
            capture_output=True,
            text=True
        )
        
        if "HFT predictions made" in result.stdout:
            print("✅ HFT mode working correctly")
            return True
        else:
            print("⚠️ HFT mode completed but check output:")
            print(result.stdout[-500:])  # Last 500 chars
            return True
            
    except subprocess.TimeoutExpired:
        print("✅ HFT mode running (stopped after 30s test)")
        return True
    except Exception as e:
        print(f"❌ HFT mode test failed: {e}")
        return False

def test_autonomous_scan():
    """Test autonomous scanning"""
    print("\n🧪 Testing Autonomous Market Scan")
    print("=" * 50)
    
    try:
        result = subprocess.run(
            [sys.executable, "main.py", "--scan"],
            timeout=60,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Autonomous scan completed successfully")
            return True
        else:
            print(f"⚠️ Scan completed with warnings: {result.stderr}")
            return True
            
    except subprocess.TimeoutExpired:
        print("✅ Autonomous scan running (stopped after 60s test)")
        return True
    except Exception as e:
        print(f"❌ Autonomous scan test failed: {e}")
        return False

def test_database_connection():
    """Test database connectivity"""
    print("\n🧪 Testing Database Connection")
    print("=" * 50)
    
    try:
        from Databases.database_service import DatabaseService
        db = DatabaseService()
        
        # Test basic query
        companies = db.execute_query("SELECT COUNT(*) as count FROM companies")
        if companies:
            print(f"✅ Database connected - {companies[0]['count']} companies in database")
            
            # Test predictions table
            predictions = db.execute_query("SELECT COUNT(*) as count FROM neural_network_predictions")
            if predictions:
                print(f"✅ Predictions table accessible - {predictions[0]['count']} predictions stored")
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 StockTrek System Testing Suite")
    print("=" * 60)
    
    tests = [
        ("Desktop Export", test_desktop_export),
        ("Database Connection", test_database_connection),
        ("HFT Mode", test_hft_mode),
        ("Autonomous Scan", test_autonomous_scan),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
    
    print("\n🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
    
    all_passed = all(results.values())
    print(f"\n🏆 Overall Status: {'✅ ALL TESTS PASSED' if all_passed else '⚠️ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 StockTrek system is fully operational!")
        print("🚀 You can now run the daemon with confidence:")
        print("   python3 daemon_service.py start")
