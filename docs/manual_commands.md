# StockTrek Manual Commands

## 🚀 **Production Commands**

### Start Full Autonomous System
```bash
# Start complete system with HFT training and auto-exports
./start_stocktrek.sh

# Or manually:
python3 main.py --daemon --hft-auto-train
```

### Quick System Check
```bash
python3 check_status.py
```

## 📊 **Individual Predictions**

### Single Stock Prediction
```bash
# Predict AAPL for 7 days
python3 main.py --predict AAPL --timeframe 7

# Predict TSLA for 30 days
python3 main.py --predict TSLA --timeframe 30

# HFT prediction (5 minutes)
python3 main.py --predict AAPL --hft --timeframe 5
```

### Batch Predictions
```bash
# Predict multiple stocks
python3 main.py --predict AAPL,MSFT,GOOGL,TSLA --timeframe 7
```

## ⚡ **HFT Training Commands**

### Manual HFT Training
```bash
# Single HFT training cycle
python3 trigger_hft_training.py

# Train on specific stocks
python3 -c "
from ETL.prediction_service import PredictionService
service = PredictionService()
service.run_hft_training_cycle(['AAPL', 'MSFT', 'GOOGL'])
"
```

## 📁 **Data Export Commands**

### Manual Data Export
```bash
# Export all data to desktop
python3 trigger_desktop_export.py

# Export specific timeframe
python3 -c "
from ETL.data_export_service import DataExportService
exporter = DataExportService()
exporter.export_all_data_to_desktop(days_back=30)
"
```

## 🔍 **Monitoring Commands**

### Check Recent Predictions
```bash
python3 -c "
from Databases.database_service import DatabaseService
db = DatabaseService()
query = '''
SELECT c.ticker, p.current_price, p.predicted_price, p.confidence_score, p.prediction_date
FROM neural_network_predictions p
JOIN companies c ON p.company_id = c.id
ORDER BY p.prediction_date DESC
LIMIT 10
'''
results = db.execute_query(query, fetch=True)
for row in results:
    print(f'{row[\"ticker\"]}: \${row[\"current_price\"]} → \${row[\"predicted_price\"]} ({row[\"confidence_score\"]}%)')
"
```

### Check HFT Performance
```bash
python3 -c "
from Databases.database_service import DatabaseService
db = DatabaseService()
query = '''
SELECT COUNT(*) as hft_predictions, AVG(confidence_score) as avg_confidence
FROM neural_network_predictions
WHERE is_hft = true AND prediction_date >= NOW() - INTERVAL '24 hours'
'''
result = db.execute_query(query, fetch=True)
if result:
    print(f'HFT Predictions (24h): {result[0][\"hft_predictions\"]}')
    print(f'Average Confidence: {result[0][\"avg_confidence\"]:.1f}%')
"
```

## 🛠️ **Maintenance Commands**

### Database Cleanup
```bash
# Clean old predictions (older than 90 days)
python3 -c "
from Databases.database_service import DatabaseService
db = DatabaseService()
query = 'DELETE FROM neural_network_predictions WHERE prediction_date < NOW() - INTERVAL \\'90 days\\''
db.execute_query(query)
print('Old predictions cleaned')
"
```

### Reset Neural Network
```bash
# Force retrain neural network
rm -rf Neural_Network/models/
python3 main.py --predict AAPL --timeframe 7
```

## 🎯 **Production Monitoring**

### System Health Check
```bash
# Complete system status
python3 check_status.py

# Check for dummy data (should return empty)
python3 -c "
from Databases.database_service import DatabaseService
db = DatabaseService()
query = '''
SELECT c.ticker, p.current_price, p.predicted_price
FROM neural_network_predictions p
JOIN companies c ON p.company_id = c.id
WHERE p.current_price = 100.0 OR p.predicted_price = 100.0
ORDER BY p.prediction_date DESC
'''
results = db.execute_query(query, fetch=True)
if results:
    print('🚨 DUMMY DATA FOUND:')
    for row in results:
        print(f'  {row[\"ticker\"]}: \${row[\"current_price\"]} → \${row[\"predicted_price\"]}')
else:
    print('✅ No dummy data found - system clean')
"
```

### Performance Metrics
```bash
# Get accuracy metrics
python3 -c "
from Databases.database_service import DatabaseService
db = DatabaseService()
query = '''
SELECT 
    COUNT(*) as total_predictions,
    AVG(CASE WHEN prediction_accuracy IS NOT NULL THEN prediction_accuracy END) as avg_accuracy,
    COUNT(CASE WHEN is_hft = true THEN 1 END) as hft_predictions
FROM neural_network_predictions
WHERE prediction_date >= NOW() - INTERVAL '7 days'
'''
result = db.execute_query(query, fetch=True)
if result:
    row = result[0]
    print(f'📊 7-Day Performance:')
    print(f'  Total Predictions: {row[\"total_predictions\"]}')
    print(f'  Average Accuracy: {row[\"avg_accuracy\"]:.1f}%' if row[\"avg_accuracy\"] else '  Average Accuracy: N/A')
    print(f'  HFT Predictions: {row[\"hft_predictions\"]}')
"
```
