# StockTrek Daemon Service Guide

## Current Behavior

When you run `python3 daemon_service.py start`, the daemon runs in **foreground mode**:

❌ **Stopping the command (Ctrl+C) will terminate the daemon**
❌ **Closing the terminal will stop the daemon**
❌ **The process does not continue in background**

## ✅ Your Data is SAFE!

**Important**: Stopping the daemon will **NOT** wipe your progress because:

- ✅ All predictions are stored in PostgreSQL database
- ✅ All learning data persists in the database  
- ✅ All company data remains in the database
- ✅ Neural network model state is preserved

When you restart the daemon, it resumes exactly where it left off.

## Solutions for Persistent Operation

### Option 1: Use `nohup` (Simplest - Works Now)

```bash
# Start daemon in background (survives terminal closure)
nohup python3 daemon_service.py start > daemon.log 2>&1 &

# Check if it's running
python3 daemon_service.py status

# Stop the daemon
python3 daemon_service.py stop
```

**Benefits:**
- ✅ Runs in background immediately
- ✅ Survives terminal closure
- ✅ Logs output to daemon.log
- ✅ Works with current code

### Option 2: Use `screen` or `tmux` (Recommended for Development)

```bash
# Start a screen session
screen -S stocktrek

# Inside screen, start the daemon
python3 daemon_service.py start

# Detach from screen (Ctrl+A, then D)
# The daemon keeps running in background

# Reattach to see logs
screen -r stocktrek

# Stop daemon
python3 daemon_service.py stop
```

**Benefits:**
- ✅ Can reattach to see live logs
- ✅ Easy to monitor and debug
- ✅ Survives terminal closure
- ✅ No code changes needed

### Option 3: System Service (Production Server)

For a real server, create a systemd service:

```bash
# Create service file
sudo nano /etc/systemd/system/stocktrek.service
```

```ini
[Unit]
Description=StockTrek Autonomous Stock Prediction System
After=network.target postgresql.service

[Service]
Type=simple
User=your_username
WorkingDirectory=/path/to/StockTrek
ExecStart=/usr/bin/python3 daemon_service.py start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable stocktrek
sudo systemctl start stocktrek

# Check status
sudo systemctl status stocktrek

# Stop service
sudo systemctl stop stocktrek
```

## Current System Capabilities

### What Happens When Daemon Runs

**Every 5 minutes:**
- Scans S&P 500 stocks during market hours
- Makes predictions for selected symbols
- Stores predictions in database

**Every hour:**
- Evaluates matured predictions (timeframe reached)
- Compares predicted vs actual prices
- Updates learning feedback

**Daily:**
- Analyzes prediction accuracy
- Updates model if needed
- Cleans up old logs

### Database Persistence

Your PostgreSQL database contains:

```sql
-- All predictions with outcomes
SELECT COUNT(*) FROM neural_network_predictions;

-- Learning feedback data
SELECT COUNT(*) FROM learning_feedback;

-- Company data
SELECT COUNT(*) FROM companies;
```

**This data persists even when daemon stops!**

## Recommended Approach

### For Development/Testing:
```bash
# Use screen for easy monitoring
screen -S stocktrek
python3 daemon_service.py start
# Ctrl+A, D to detach
```

### For Production:
```bash
# Use nohup for simple background operation
nohup python3 daemon_service.py start > daemon.log 2>&1 &
```

### For Server Deployment:
- Use systemd service (Option 3)
- Set up proper logging
- Configure automatic restarts

## Monitoring Your Daemon

```bash
# Check if daemon is running
python3 daemon_service.py status

# View recent predictions
python3 main.py --status

# Check logs
tail -f stocktrek.log

# Check daemon logs (if using nohup)
tail -f daemon.log
```

## Key Points

1. **Your progress is NEVER lost** - everything is in the database
2. **The daemon can be stopped and restarted safely**
3. **Use `nohup` or `screen` for background operation**
4. **The system is designed to handle interruptions gracefully**

## Quick Start for Background Operation

```bash
# Start in background right now
nohup python3 daemon_service.py start > daemon.log 2>&1 &

# Verify it's running
python3 daemon_service.py status

# Check logs
tail -f daemon.log
```

This will keep your daemon running even if you close the terminal!
