INFO:__main__:🚀 Starting StockTrek Daemon
INFO:__main__:Initializing StockTrek services...
ERROR:Neural_Network.neural_network_service:Error loading neural network: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
INFO:Neural_Network.neural_network_service:Creating fallback model for basic functionality
INFO:Neural_Network.neural_network_service:Fallback regression model created successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
ERROR:Neural_Network.neural_network_service:Error loading neural network: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
INFO:Neural_Network.neural_network_service:Creating fallback model for basic functionality
INFO:Neural_Network.neural_network_service:Fallback regression model created successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
INFO:ETL.prediction_validator:🛡️ Prediction Validator initialized
INFO:ETL.prediction_validator:   Max price change: ±50.0%
INFO:ETL.prediction_validator:   Min price threshold: $0.01
INFO:ETL.prediction_validator:   Ollama endpoint: http://localhost:11434
INFO:ETL.prediction_service:Prediction service initialized
ERROR:Neural_Network.neural_network_service:Error loading neural network: <class 'numpy.random._mt19937.MT19937'> is not a known BitGenerator module.
INFO:Neural_Network.neural_network_service:Creating fallback model for basic functionality
INFO:Neural_Network.neural_network_service:Fallback regression model created successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
INFO:ETL.prediction_validator:🛡️ Prediction Validator initialized
INFO:ETL.prediction_validator:   Max price change: ±50.0%
INFO:ETL.prediction_validator:   Min price threshold: $0.01
INFO:ETL.prediction_validator:   Ollama endpoint: http://localhost:11434
INFO:ETL.prediction_service:Prediction service initialized
INFO:ETL.autonomous_scanner:Autonomous scanner initialized
INFO:ETL.backtesting_service:Backtesting service initialized
INFO:__main__:✅ All services initialized successfully
INFO:__main__:⚡ HFT training mode enabled
INFO:__main__:📅 Scheduled tasks configured
INFO:__main__:🔄 Background threads started
INFO:__main__:✅ StockTrek Daemon started successfully
INFO:__main__:🔍 Running autonomous market scan
ERROR:__main__:Error in autonomous scan: scan_market() missing 1 required positional argument: 'symbols'
INFO:__main__:⚡ Running HFT training cycle
INFO:ETL.prediction_service:⚡ Making HFT prediction for AAPL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for AAPL: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for MSFT (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for MSFT: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for GOOGL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for GOOGL: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:__main__:⚡ HFT cycle complete: 0 predictions made
INFO:__main__:🔍 Running autonomous market scan
ERROR:__main__:Error in autonomous scan: scan_market() missing 1 required positional argument: 'symbols'
INFO:__main__:⚡ Running HFT training cycle
INFO:ETL.prediction_service:⚡ Making HFT prediction for AAPL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for AAPL: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for MSFT (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for MSFT: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for GOOGL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for GOOGL: predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:__main__:⚡ HFT cycle complete: 0 predictions made
INFO:__main__:Received signal 15, shutting down...
INFO:__main__:🛑 Shutting down StockTrek Daemon
INFO:__main__:✅ Daemon shutdown complete
INFO:__main__:🚀 Starting StockTrek Daemon
INFO:__main__:Initializing StockTrek services...
INFO:Neural_Network.neural_network_service:Neural network model loaded successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
INFO:Neural_Network.neural_network_service:Neural network model loaded successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
INFO:ETL.prediction_validator:🛡️ Prediction Validator initialized
INFO:ETL.prediction_validator:   Max price change: ±50.0%
INFO:ETL.prediction_validator:   Min price threshold: $0.01
INFO:ETL.prediction_validator:   Ollama endpoint: http://localhost:11434
INFO:ETL.prediction_service:Prediction service initialized
INFO:Neural_Network.neural_network_service:Neural network model loaded successfully
INFO:Neural_Network.neural_network_service:Neural network services initialized successfully
INFO:ETL.prediction_validator:🛡️ Prediction Validator initialized
INFO:ETL.prediction_validator:   Max price change: ±50.0%
INFO:ETL.prediction_validator:   Min price threshold: $0.01
INFO:ETL.prediction_validator:   Ollama endpoint: http://localhost:11434
INFO:ETL.prediction_service:Prediction service initialized
INFO:ETL.autonomous_scanner:Autonomous scanner initialized
INFO:ETL.backtesting_service:Backtesting service initialized
INFO:__main__:✅ All services initialized successfully
INFO:__main__:⚡ HFT training mode enabled
INFO:__main__:📅 Scheduled tasks configured
INFO:__main__:🔄 Background threads started
INFO:__main__:✅ StockTrek Daemon started successfully
INFO:__main__:🔍 Running autonomous market scan
ERROR:__main__:Error in autonomous scan: AutonomousScanner.scan_market() missing 1 required positional argument: 'symbols'
INFO:__main__:⚡ Running HFT training cycle
INFO:ETL.prediction_service:⚡ Making HFT prediction for AAPL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for AAPL: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for MSFT (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for MSFT: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for GOOGL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for GOOGL: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:__main__:⚡ HFT cycle complete: 0 predictions made
INFO:__main__:🔍 Running autonomous market scan
ERROR:__main__:Error in autonomous scan: AutonomousScanner.scan_market() missing 1 required positional argument: 'symbols'
INFO:__main__:⚡ Running HFT training cycle
INFO:ETL.prediction_service:⚡ Making HFT prediction for AAPL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for AAPL: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for MSFT (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for MSFT: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:ETL.prediction_service:⚡ Making HFT prediction for GOOGL (5min)
ERROR:ETL.prediction_service:❌ HFT prediction error for GOOGL: NeuralNetworkService.predict_stock_with_etl() got an unexpected keyword argument 'ticker'
INFO:__main__:⚡ HFT cycle complete: 0 predictions made
INFO:__main__:Received signal 15, shutting down...
INFO:__main__:🛑 Shutting down StockTrek Daemon
INFO:__main__:✅ Daemon shutdown complete
